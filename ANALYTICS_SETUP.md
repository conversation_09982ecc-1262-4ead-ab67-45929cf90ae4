# Analytics Configuration

This document explains how to configure Google Analytics in your reporting system to avoid cookie warnings in development.

## Cookie Warnings Fix

The cookie warnings you were seeing (`<PERSON><PERSON> "_ga" does not have a proper "SameSite" attribute value`) have been fixed by:

1. **Disabling Analytics in Development by Default**: Analytics are now disabled in development mode to prevent cookie warnings during local development.

2. **Proper SameSite Configuration**: When analytics are enabled, cookies are configured with proper `SameSite=Lax` attributes.

3. **Environment-Based Control**: You can control analytics behavior through environment variables.

## Environment Variables

In your `.env.local` file, you can control analytics behavior:

```env
# Set to 'true' to enable Google Analytics in development (will show cookie warnings)
# Set to 'false' or remove to disable analytics in development (recommended)
NEXT_PUBLIC_ENABLE_ANALYTICS=false

# Set to 'true' to enable Firebase debug logging in development
NEXT_PUBLIC_DEBUG_FIREBASE=false
```

## Analytics Behavior

### Development Mode (NODE_ENV=development)
- **Default**: Analytics disabled, no cookie warnings
- **With NEXT_PUBLIC_ENABLE_ANALYTICS=true**: Analytics enabled with `SameSite=Lax` cookies for localhost

### Production Mode (NODE_ENV=production)
- **Always enabled** with proper `SameSite=Lax;Secure` cookie configuration
- Includes GDPR-compliant settings like `anonymize_ip: true`

## Testing Analytics

To test analytics in development:

1. Set `NEXT_PUBLIC_ENABLE_ANALYTICS=true` in your `.env.local`
2. Restart your development server
3. Analytics will be enabled (you may see cookie warnings, but they'll be properly configured)

To disable analytics completely in development (recommended):

1. Set `NEXT_PUBLIC_ENABLE_ANALYTICS=false` or remove the variable
2. Restart your development server
3. No analytics cookies will be set, no warnings

## Cookie Configuration Details

The analytics implementation now uses:

- **SameSite=Lax**: Compatible with most browsers and use cases
- **Secure flag**: Only in production (HTTPS)
- **Domain**: `localhost` in development, `auto` in production
- **Expires**: 2 years (standard Google Analytics)
- **Anonymize IP**: Enabled for GDPR compliance

## Files Modified

1. `src/lib/firebase.ts` - Updated analytics initialization
2. `src/app/layout.tsx` - Added proper Google Analytics script with cookie configuration
3. `src/components/analytics/analytics-provider.tsx` - Created analytics provider component
4. `.env.local` - Added analytics control variables

## Troubleshooting

If you still see cookie warnings:

1. Make sure `NEXT_PUBLIC_ENABLE_ANALYTICS=false` in `.env.local`
2. Restart your development server
3. Clear your browser cookies for localhost:3000
4. Refresh the page

The warnings should be gone since analytics will be completely disabled in development mode.
