# Firebase Permissions Fix Guide

The Firebase sync feature is showing "Missing or insufficient permissions" errors because the new collections (`external_firebase_connections` and `sync_logs`) need to be added to your Firestore security rules.

## 🚨 Current Issue

The error occurs because:
1. New collections were added for Firebase sync functionality
2. Firestore security rules don't allow access to these new collections
3. The app tries to read/write to these collections but gets permission denied

## 🔧 Quick Fix Options

### Option 1: Deploy Updated Rules (Recommended)

I've already updated the `firestore.rules` file with the necessary permissions. You need to deploy them:

```bash
# 1. Login to Firebase (if not already logged in)
firebase login

# 2. Set the project (use your project ID)
firebase use reporting-system-81bf8

# 3. Deploy the updated rules
firebase deploy --only firestore:rules
```

Or use the helper script I created:
```bash
node scripts/deploy-firestore-rules.js
```

### Option 2: Manual Rules Update via Firebase Console

If you prefer to update rules manually:

1. Go to [Firebase Console](https://console.firebase.google.com)
2. Select your project: `reporting-system-81bf8`
3. Go to **Firestore Database** → **Rules**
4. Add these rules before the final `match /{document=**}` block:

```javascript
// External Firebase connections collection - users can manage their own connections
match /external_firebase_connections/{connectionId} {
  allow read, write: if request.auth != null && request.auth.uid == resource.data.user_id;
  allow create: if request.auth != null && request.auth.uid == request.resource.data.user_id
    && request.resource.data.keys().hasAll(['name', 'project_id', 'user_id', 'config']);
}

// Sync logs collection - users can read their own sync logs, system can create them
match /sync_logs/{syncLogId} {
  allow read: if request.auth != null && request.auth.uid == resource.data.user_id;
  allow create: if request.auth != null && request.resource.data.keys().hasAll(['connection_id', 'user_id', 'sync_type']);
}
```

5. Click **Publish**

### Option 3: Temporary Development Rules (Quick Test)

For immediate testing, you can temporarily use more permissive rules:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // TEMPORARY - Allow authenticated users to read/write all documents
    match /{document=**} {
      allow read, write: if request.auth != null;
    }
  }
}
```

⚠️ **Warning**: Only use Option 3 for development/testing. Revert to proper rules for production.

## 📋 Step-by-Step Instructions

### Step 1: Check Firebase CLI Setup
```bash
# Check if Firebase CLI is installed
firebase --version

# If not installed:
npm install -g firebase-tools

# Login to Firebase
firebase login
```

### Step 2: Set Project
```bash
# List available projects
firebase projects:list

# Use your project
firebase use reporting-system-81bf8
```

### Step 3: Deploy Rules
```bash
# Deploy the updated rules
firebase deploy --only firestore:rules
```

### Step 4: Verify Deployment
1. Check Firebase Console → Firestore → Rules
2. Verify the new rules are present
3. Refresh your browser and test the Firebase sync page

## 🔍 Troubleshooting

### If you get "Not logged in" error:
```bash
firebase login --reauth
```

### If you get "Project not found" error:
```bash
firebase projects:list
# Use the correct project ID from the list
firebase use <your-project-id>
```

### If rules deployment fails:
1. Check your internet connection
2. Verify you have owner/editor permissions on the Firebase project
3. Try deploying via Firebase Console (Option 2)

## ✅ After Fixing

Once the rules are deployed:

1. **Refresh your browser** - Clear any cached errors
2. **Test the Firebase sync page** - Go to `/dashboard/data/firebase-sync`
3. **Check browser console** - Errors should be gone
4. **Try adding a connection** - Test the full functionality

## 📁 Files Updated

The following files have been updated with the necessary changes:

- ✅ `firestore.rules` - Added security rules for new collections
- ✅ `scripts/deploy-firestore-rules.js` - Helper script for deployment
- ✅ `scripts/setup-database.js` - Updated to create new collections

## 🔐 Security Notes

The updated rules ensure:
- Users can only access their own Firebase connections
- Users can only read their own sync logs
- Proper validation of required fields on creation
- No unauthorized access to other users' data

## 🚀 Next Steps

After fixing the permissions:
1. Test the Firebase sync functionality
2. Try adding a new Firebase connection
3. Monitor the sync dashboard for real-time updates
4. Check sync logs for activity tracking

The Firebase sync feature should work perfectly once the security rules are deployed!
