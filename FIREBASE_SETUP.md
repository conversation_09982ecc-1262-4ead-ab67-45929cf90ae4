# Firebase Migration Setup Guide

This guide will help you set up Firebase for your reporting system after migrating from Supabase.

## 1. Create a Firebase Project

1. Go to [Firebase Console](https://console.firebase.google.com)
2. Click "Create a project" or "Add project"
3. Enter your project name (e.g., "reporting-system")
4. Choose whether to enable Google Analytics (optional)
5. Click "Create project"

## 2. Enable Required Services

### Enable Authentication
1. In your Firebase project, go to **Authentication**
2. Click "Get started"
3. Go to the **Sign-in method** tab
4. Enable **Email/Password** authentication
5. Optionally enable other providers (Google, GitHub, etc.)

### Enable Firestore Database
1. Go to **Firestore Database**
2. Click "Create database"
3. Choose "Start in test mode" (we'll deploy security rules later)
4. Select a location for your database
5. Click "Done"

### Enable Storage (Optional)
1. Go to **Storage**
2. Click "Get started"
3. Review security rules and click "Next"
4. Choose a location and click "Done"

## 3. Get Firebase Configuration

1. Go to **Project Settings** (gear icon)
2. Scroll down to "Your apps"
3. Click the web icon (`</>`) to add a web app
4. Enter your app name and click "Register app"
5. Copy the Firebase configuration object

## 4. Update Environment Variables

Update your `.env.local` file with your Firebase configuration:

```env
NEXT_PUBLIC_FIREBASE_API_KEY=your_api_key_here
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your_project_id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your_project.appspot.com
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=123456789
NEXT_PUBLIC_FIREBASE_APP_ID=1:123456789:web:abcdef123456
```

## 5. Deploy Firestore Security Rules

1. Install Firebase CLI: `npm install -g firebase-tools`
2. Login to Firebase: `firebase login`
3. Initialize Firebase in your project: `firebase init`
   - Select "Firestore" and "Hosting" (optional)
   - Choose your existing project
   - Accept default Firestore rules file (`firestore.rules`)
   - Accept default Firestore indexes file (`firestore.indexes.json`)
4. Deploy rules: `firebase deploy --only firestore:rules`

## 6. Test the Setup

Run the test connection script:
```bash
node scripts/test-connection.js
```

If successful, you should see:
- ✅ Test document created successfully!
- ✅ Connection test completed!

## 7. Start Your Application

```bash
npm run dev
```

Your application should now be running with Firebase!

## Key Differences from Supabase

### Authentication
- **Supabase**: `supabase.auth.signInWithPassword()`
- **Firebase**: `signInWithEmailAndPassword(auth, email, password)`

### Database
- **Supabase**: SQL queries with `supabase.from('table').select()`
- **Firebase**: NoSQL with `collection(db, 'collection').get()`

### Real-time Updates
- **Supabase**: `supabase.from('table').on('INSERT', callback)`
- **Firebase**: `onSnapshot(collection(db, 'collection'), callback)`

## Security Rules

The `firestore.rules` file contains security rules that:
- Allow users to read/write their own data
- Prevent unauthorized access
- Validate data structure and types
- Implement role-based access control

## Troubleshooting

### Common Issues

1. **"Missing Firebase environment variables"**
   - Make sure all Firebase config variables are set in `.env.local`
   - Restart your development server after updating environment variables

2. **"Permission denied" errors**
   - Deploy your Firestore security rules: `firebase deploy --only firestore:rules`
   - Check that your rules allow the operation you're trying to perform

3. **"Firebase app not initialized"**
   - Make sure your Firebase configuration is correct
   - Check that all required environment variables are present

4. **Authentication not working**
   - Verify Email/Password authentication is enabled in Firebase Console
   - Check that your auth domain is correct in the configuration

### Getting Help

- [Firebase Documentation](https://firebase.google.com/docs)
- [Firestore Security Rules](https://firebase.google.com/docs/firestore/security/get-started)
- [Firebase Authentication](https://firebase.google.com/docs/auth)

## Migration Checklist

- [ ] Firebase project created
- [ ] Authentication enabled
- [ ] Firestore database created
- [ ] Environment variables updated
- [ ] Security rules deployed
- [ ] Connection test passed
- [ ] Application starts successfully
- [ ] User registration works
- [ ] User login works
- [ ] Data operations work
