# Firebase Real-time Data Sync

This feature enables real-time data synchronization between multiple Firebase projects, allowing you to aggregate data from different sources into your reporting system.

## 🚀 Features

### ✅ Multi-Project Connections
- Connect to multiple external Firebase projects
- Secure configuration management
- Connection testing and validation
- Real-time status monitoring

### ✅ Real-time Data Sync
- **Pull Sync**: Import data from external projects to local database
- **Push Sync**: Export local data to external projects  
- **Bidirectional Sync**: Two-way synchronization with conflict resolution
- Real-time listeners for instant data updates

### ✅ Advanced Sync Configuration
- **Collection Mapping**: Specify which collections to sync
- **Field Transformations**: Transform data during sync
- **Filtering**: Apply filters to sync only specific documents
- **Conflict Resolution**: Handle data conflicts with multiple strategies

### ✅ Security & Validation
- User permission checks and rate limiting
- Firebase configuration validation
- Data sanitization and security checks
- Duplicate connection prevention

### ✅ Monitoring & Analytics
- Real-time sync dashboard
- Sync history and logs
- Performance metrics and statistics
- Error tracking and reporting

## 🔧 How It Works

### 1. Connection Setup
```typescript
// Add a new Firebase connection
const connection = {
  name: "Production Database",
  projectId: "my-external-project",
  config: {
    apiKey: "AIza...",
    authDomain: "my-project.firebaseapp.com",
    // ... other Firebase config
  },
  syncSettings: {
    direction: "pull", // or "push" or "bidirectional"
    collections: ["users", "orders", "products"],
    frequency: "realtime", // or "hourly", "daily", "manual"
    conflictResolution: "source_wins"
  }
}
```

### 2. Real-time Sync Engine
The sync engine uses Firebase's real-time listeners to detect changes and automatically synchronize data:

```typescript
// Start real-time sync
await firebaseSyncEngine.startSync(connectionId, syncRules, "pull")

// The engine will:
// 1. Set up real-time listeners on external collections
// 2. Transform and validate incoming data
// 3. Apply changes to local database
// 4. Handle conflicts according to resolution strategy
// 5. Log all operations for monitoring
```

### 3. Data Transformation
Configure how data is transformed during sync:

```typescript
const syncRule = {
  sourceCollection: "external_users",
  targetCollection: "users",
  fieldMappings: {
    "user_id": "id",
    "full_name": "name"
  },
  transformations: [
    {
      field: "created_at",
      transform: (value) => new Date(value).toISOString()
    }
  ]
}
```

## 📊 Dashboard Features

### Connection Management
- View all Firebase connections
- Monitor connection status (connected/disconnected/error)
- Test connections before activation
- Edit connection settings

### Sync Monitoring
- Real-time sync status for each connection
- Success/failure rates and statistics
- Document processing metrics
- Active sync session monitoring

### Sync History
- Complete log of all sync operations
- Detailed error messages and debugging info
- Performance metrics (duration, throughput)
- Filterable by connection, status, and time range

## 🔒 Security Features

### Authentication & Authorization
- User-based connection ownership
- Permission validation for all operations
- Rate limiting to prevent abuse
- Secure credential storage

### Data Validation
- Firebase configuration validation
- Collection name validation
- Data type checking and sanitization
- Duplicate connection prevention

### Error Handling
- Comprehensive error logging
- Automatic retry mechanisms
- Graceful failure handling
- User-friendly error messages

## 🚦 Getting Started

### 1. Navigate to Firebase Sync
Go to **Dashboard → Import → Firebase Projects** or directly to `/dashboard/data/firebase-sync`

### 2. Add Your First Connection
1. Click "Add Connection"
2. Enter connection details:
   - **Name**: Descriptive name for the connection
   - **Firebase Config**: Copy from your external project settings
   - **Sync Settings**: Configure direction, collections, and frequency
3. Test the connection
4. Save and activate

### 3. Monitor Sync Activity
- View the **Dashboard** tab for real-time metrics
- Check **Connections** tab for individual connection status
- Review **Sync Logs** for detailed operation history

## ⚙️ Configuration Options

### Sync Direction
- **Pull**: External → Local (import data)
- **Push**: Local → External (export data)  
- **Bidirectional**: Two-way sync (import and export)

### Sync Frequency
- **Real-time**: Instant synchronization using Firebase listeners
- **Hourly**: Scheduled sync every hour
- **Daily**: Scheduled sync once per day
- **Manual**: Sync only when manually triggered

### Conflict Resolution
- **Source Wins**: External data overwrites local data
- **Target Wins**: Local data is preserved
- **Merge**: Attempt to merge conflicting fields
- **Manual**: Require manual resolution of conflicts

## 🔍 Troubleshooting

### Common Issues

**Connection Test Fails**
- Verify Firebase configuration is correct
- Check Firestore security rules allow read/write access
- Ensure API key has necessary permissions

**Sync Not Working**
- Check connection status in dashboard
- Verify collections exist in both projects
- Review sync logs for error messages

**Performance Issues**
- Consider reducing sync frequency for large datasets
- Limit number of collections being synced
- Use filters to sync only necessary documents

### Error Codes
- `permission-denied`: Check Firestore security rules
- `invalid-api-key`: Verify Firebase API key
- `project-not-found`: Check Firebase project ID
- `rate-limit-exceeded`: Wait before creating more connections

## 📈 Best Practices

### Security
- Use separate Firebase projects for different environments
- Implement proper Firestore security rules
- Regularly rotate API keys
- Monitor sync logs for suspicious activity

### Performance
- Start with pull-only sync for initial data import
- Use real-time sync only for critical collections
- Implement data archiving for large historical datasets
- Monitor sync performance and adjust frequency as needed

### Data Management
- Plan your collection structure before setting up sync
- Use consistent field naming across projects
- Implement data validation in both source and target
- Regular backup of synchronized data

## 🛠️ Technical Architecture

### Components
- **MultiFirebaseManager**: Manages connections to multiple Firebase projects
- **FirebaseSyncEngine**: Handles real-time synchronization logic
- **FirebaseSyncSecurity**: Provides security and validation
- **SyncDashboard**: Real-time monitoring interface

### Data Flow
1. User configures external Firebase connection
2. Connection is validated and stored securely
3. Sync engine establishes real-time listeners
4. Changes are detected and processed
5. Data is transformed and applied to target
6. Operations are logged for monitoring

This Firebase sync feature provides a powerful way to aggregate data from multiple Firebase projects while maintaining security, performance, and reliability.
