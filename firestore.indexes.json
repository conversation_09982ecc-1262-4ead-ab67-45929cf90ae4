{"indexes": [{"collectionGroup": "data_sources", "queryScope": "COLLECTION", "fields": [{"fieldPath": "user_id", "order": "ASCENDING"}, {"fieldPath": "created_at", "order": "DESCENDING"}]}, {"collectionGroup": "datasets", "queryScope": "COLLECTION", "fields": [{"fieldPath": "data_source_id", "order": "ASCENDING"}, {"fieldPath": "created_at", "order": "DESCENDING"}]}, {"collectionGroup": "reports", "queryScope": "COLLECTION", "fields": [{"fieldPath": "user_id", "order": "ASCENDING"}, {"fieldPath": "created_at", "order": "DESCENDING"}]}, {"collectionGroup": "reports", "queryScope": "COLLECTION", "fields": [{"fieldPath": "is_template", "order": "ASCENDING"}, {"fieldPath": "created_at", "order": "DESCENDING"}]}, {"collectionGroup": "report_recommendations", "queryScope": "COLLECTION", "fields": [{"fieldPath": "dataset_id", "order": "ASCENDING"}, {"fieldPath": "confidence_score", "order": "DESCENDING"}]}, {"collectionGroup": "audit_logs", "queryScope": "COLLECTION", "fields": [{"fieldPath": "user_id", "order": "ASCENDING"}, {"fieldPath": "created_at", "order": "DESCENDING"}]}], "fieldOverrides": []}