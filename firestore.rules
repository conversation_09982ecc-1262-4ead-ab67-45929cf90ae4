rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    // DEVELOPMENT RULES - More permissive for testing
    // TODO: Tighten these rules for production
    // Users collection - users can read/write their own document
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
      allow create: if request.auth != null && request.auth.uid == userId
        && request.resource.data.id == userId;
    }

    // Data sources collection - users can manage their own data sources
    match /data_sources/{dataSourceId} {
      allow read, write: if request.auth != null && request.auth.uid == resource.data.user_id;
      allow create: if request.auth != null && request.auth.uid == request.resource.data.user_id
        && request.resource.data.keys().hasAll(['name', 'type', 'user_id']);
    }

    // Datasets collection - users can manage datasets for their data sources
    match /datasets/{datasetId} {
      allow read, write: if request.auth != null &&
        exists(/databases/$(database)/documents/data_sources/$(resource.data.data_source_id)) &&
        get(/databases/$(database)/documents/data_sources/$(resource.data.data_source_id)).data.user_id == request.auth.uid;
      allow create: if request.auth != null &&
        exists(/databases/$(database)/documents/data_sources/$(request.resource.data.data_source_id)) &&
        get(/databases/$(database)/documents/data_sources/$(request.resource.data.data_source_id)).data.user_id == request.auth.uid
        && request.resource.data.keys().hasAll(['name', 'data_source_id']);
    }

    // Reports collection - users can manage their own reports
    match /reports/{reportId} {
      allow read, write: if request.auth != null && request.auth.uid == resource.data.user_id;
      allow create: if request.auth != null && request.auth.uid == request.resource.data.user_id;
      // Allow reading public reports
      allow read: if resource.data.is_public == true;
    }

    // Report recommendations collection - users can read recommendations for their reports
    match /report_recommendations/{recommendationId} {
      allow read: if request.auth != null &&
        exists(/databases/$(database)/documents/reports/$(resource.data.report_id)) &&
        get(/databases/$(database)/documents/reports/$(resource.data.report_id)).data.user_id == request.auth.uid;
      allow create: if request.auth != null &&
        exists(/databases/$(database)/documents/reports/$(request.resource.data.report_id)) &&
        get(/databases/$(database)/documents/reports/$(request.resource.data.report_id)).data.user_id == request.auth.uid;
    }

    // Audit logs collection - users can read their own audit logs, system can create them
    match /audit_logs/{auditLogId} {
      allow read: if request.auth != null && request.auth.uid == resource.data.user_id;
      allow create: if request.auth != null && request.resource.data.keys().hasAll(['user_id', 'action', 'resource_type']);
    }

    // External Firebase connections collection - users can manage their own connections
    match /external_firebase_connections/{connectionId} {
      allow read, write: if request.auth != null && request.auth.uid == resource.data.user_id;
      allow create: if request.auth != null && request.auth.uid == request.resource.data.user_id
        && request.resource.data.keys().hasAll(['name', 'project_id', 'user_id', 'config']);
    }

    // Sync logs collection - users can read their own sync logs, system can create them
    match /sync_logs/{syncLogId} {
      allow read: if request.auth != null && request.auth.uid == resource.data.user_id;
      allow create: if request.auth != null && request.resource.data.keys().hasAll(['connection_id', 'user_id', 'sync_type']);
    }

    // Synced collections - allow authenticated users to read/write to collections that contain synced data
    // These collections are identified by having documents with _sync_source metadata
    match /{collection}/{document} {
      allow read, write: if request.auth != null &&
        (
          // Allow if the document has sync metadata indicating it was synced by this user
          (resource != null && resource.data.keys().hasAny(['_sync_source', '_sync_timestamp'])) ||
          // Allow if we're creating a document with sync metadata
          (request.resource != null && request.resource.data.keys().hasAny(['_sync_source', '_sync_timestamp']))
        );
    }

    // Deny all other access
    match /{document=**} {
      allow read, write: if false;
    }
  }
}