#!/usr/bin/env node

/**
 * <PERSON>ript to deploy Firestore security rules
 * This script helps deploy the updated Firestore rules for Firebase sync functionality
 */

const { execSync } = require('child_process')
const fs = require('fs')
const path = require('path')

// Load environment variables
require('dotenv').config({ path: '.env.local' })

const projectId = process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID

if (!projectId) {
  console.error('❌ Missing Firebase project ID')
  console.error('Make sure NEXT_PUBLIC_FIREBASE_PROJECT_ID is set in .env.local')
  process.exit(1)
}

console.log('🚀 Deploying Firestore security rules...')
console.log(`📍 Project ID: ${projectId}`)

try {
  // Check if Firebase CLI is installed
  try {
    execSync('firebase --version', { stdio: 'pipe' })
  } catch (error) {
    console.error('❌ Firebase CLI is not installed')
    console.error('Install it with: npm install -g firebase-tools')
    process.exit(1)
  }

  // Check if user is logged in
  try {
    execSync('firebase projects:list', { stdio: 'pipe' })
  } catch (error) {
    console.error('❌ Not logged in to Firebase')
    console.error('Run: firebase login')
    process.exit(1)
  }

  // Deploy the rules
  console.log('📝 Deploying Firestore rules...')
  execSync(`firebase deploy --only firestore:rules --project ${projectId}`, { 
    stdio: 'inherit',
    cwd: process.cwd()
  })

  console.log('✅ Firestore rules deployed successfully!')
  console.log('')
  console.log('🔧 The following collections are now accessible:')
  console.log('   • external_firebase_connections')
  console.log('   • sync_logs')
  console.log('')
  console.log('🔄 You can now refresh your browser to test the Firebase sync feature')

} catch (error) {
  console.error('❌ Failed to deploy Firestore rules:', error.message)
  console.error('')
  console.error('📋 Manual deployment steps:')
  console.error('1. Run: firebase login')
  console.error(`2. Run: firebase use ${projectId}`)
  console.error('3. Run: firebase deploy --only firestore:rules')
  process.exit(1)
}
