const admin = require('firebase-admin')
const fs = require('fs')
const path = require('path')

// Load environment variables
require('dotenv').config({ path: '.env.local' })

const projectId = process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID

if (!projectId) {
  console.error('❌ Missing Firebase environment variables')
  console.error('Make sure NEXT_PUBLIC_FIREBASE_PROJECT_ID is set in .env.local')
  process.exit(1)
}

// Initialize Firebase Admin SDK
if (!admin.apps.length) {
  admin.initializeApp({
    projectId: projectId,
  })
}

const db = admin.firestore()

async function setupDatabase() {
  console.log('🚀 Setting up Firestore database...')

  try {
    // Create collections with initial documents to establish structure
    console.log('📝 Creating Firestore collections...')

    const collections = [
      'users',
      'data_sources',
      'datasets',
      'reports',
      'report_recommendations',
      'audit_logs',
      'external_firebase_connections',
      'sync_logs'
    ]

    for (const collectionName of collections) {
      console.log(`   Creating collection: ${collectionName}`)

      // Create a temporary document to establish the collection
      const tempDocRef = db.collection(collectionName).doc('_temp')
      await tempDocRef.set({
        _temp: true,
        created_at: admin.firestore.FieldValue.serverTimestamp()
      })

      // Delete the temporary document
      await tempDocRef.delete()

      console.log(`   ✅ Collection ${collectionName} created`)
    }

    console.log('✅ Firestore database setup completed!')

    // Test the connection
    console.log('🔍 Testing Firestore connection...')
    const testDoc = await db.collection('users').limit(1).get()
    console.log('✅ Firestore connection successful!')

    console.log('')
    console.log('🎯 Next steps:')
    console.log('1. Deploy Firestore security rules: firebase deploy --only firestore:rules')
    console.log('2. Your Firebase project is ready to use!')
    console.log('')

  } catch (error) {
    console.error('❌ Error setting up Firestore:', error.message)
    process.exit(1)
  }
}

// This function is no longer needed for Firestore
// Collections are created dynamically when documents are added

// Run the setup
setupDatabase()
