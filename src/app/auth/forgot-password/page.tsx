import { ForgotPasswordForm } from '@/components/auth/forgot-password-form'
import { BarChart3, Building2 } from 'lucide-react'
import { Metadata } from 'next'

export const metadata: Metadata = {
  title: "Password Reset - Internal Analytics Platform",
  description: "Reset your password for the company's internal analytics platform",
}

export default function ForgotPasswordPage() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 to-blue-50 px-4">
      <div className="w-full max-w-md space-y-8">
        <div className="text-center">
          <div className="flex justify-center mb-4">
            <BarChart3 className="h-16 w-16 text-blue-600" />
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Internal Analytics Platform
          </h1>
          <div className="flex items-center justify-center space-x-2 text-sm text-gray-600 mb-4">
            <Building2 className="h-4 w-4" />
            <span>Company Internal System</span>
          </div>
        </div>

        <ForgotPasswordForm />

        <div className="text-center">
          <p className="text-xs text-gray-500">
            Having trouble? Contact IT <NAME_EMAIL>
          </p>
        </div>
      </div>
    </div>
  )
}
