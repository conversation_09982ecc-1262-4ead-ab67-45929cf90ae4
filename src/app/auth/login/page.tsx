import { LoginForm } from '@/components/auth/login-form'
import { InteractiveBackgroundCircles } from '@/components/ui/interactive-background-circles'
import { Metadata } from 'next'
import { BarChart3, Shield, Users } from 'lucide-react'

export const metadata: Metadata = {
  title: "Login - Analytics Platform",
  description: "Sign in to access your analytics dashboard",
}

export default function LoginPage() {
  return (
    <div className="min-h-screen flex items-center justify-center relative bg-gradient-to-br from-slate-100 via-gray-50 to-slate-200">
      {/* Interactive background elements */}
      <InteractiveBackgroundCircles />

      <div className="bg-white/80 backdrop-blur-lg rounded-3xl shadow-2xl overflow-hidden max-w-6xl w-full mx-4 flex flex-col lg:flex-row relative z-10 border border-white/20" style={{minHeight: '700px'}}>
        {/* Left Section - Enhanced Slate with modern elements */}
        <div className="flex-1 p-8 lg:p-16 flex flex-col justify-center items-center text-white relative min-h-[300px] lg:min-h-auto bg-gradient-to-br from-slate-700 via-slate-800 to-gray-900">
          {/* SVG curved border for desktop */}
          <svg
            className="absolute top-0 right-0 h-full w-20 hidden lg:block"
            viewBox="0 0 100 400"
            preserveAspectRatio="none"
            style={{ transform: 'translateX(50%)' }}
          >
            <path
              d="M 0 0 Q 50 100 30 200 Q 50 300 0 400 L 100 400 L 100 0 Z"
              fill="white"
            />
          </svg>

          <div className="text-center relative z-10 max-w-md">
            <h1 className="text-3xl lg:text-5xl xl:text-6xl font-bold mb-4 lg:mb-6 bg-gradient-to-r from-white to-slate-200 bg-clip-text text-transparent">
              Welcome Back!
            </h1>
            <p className="text-slate-200 mb-6 lg:mb-10 text-lg lg:text-xl leading-relaxed px-4 lg:px-0">
              Access your analytics dashboard and unlock powerful insights for your business
            </p>

            {/* Feature highlights - simplified on mobile */}
            <div className="space-y-3 lg:space-y-4 mb-6 lg:mb-10">
              <div className="flex items-center justify-center space-x-3 text-purple-100">
                <Users className="h-4 w-4 lg:h-5 lg:w-5" />
                <span className="text-sm">Team Collaboration</span>
              </div>
              <div className="flex items-center justify-center space-x-3 text-purple-100">
                <BarChart3 className="h-4 w-4 lg:h-5 lg:w-5" />
                <span className="text-sm">Advanced Analytics</span>
              </div>
              <div className="flex items-center justify-center space-x-3 text-purple-100">
                <Shield className="h-4 w-4 lg:h-5 lg:w-5" />
                <span className="text-sm">Secure & Reliable</span>
              </div>
            </div>
          </div>
        </div>

        {/* Right Section - Enhanced form area */}
        <div className="flex-1 p-6 lg:p-16 flex items-center justify-center bg-gradient-to-br from-white to-gray-50">
          <LoginForm />
        </div>
      </div>
    </div>
  )
}
