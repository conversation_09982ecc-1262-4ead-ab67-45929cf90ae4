import { BarChart3, Building2, Mail, Phone } from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import Link from 'next/link'
import { Metadata } from 'next'

export const metadata: Metadata = {
  title: "Access Request - Internal Analytics Platform",
  description: "Request access to the company's internal analytics platform",
}

export default function AccessRequestPage() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 to-blue-50 px-4">
      <div className="w-full max-w-md space-y-8">
        <div className="text-center">
          <div className="flex justify-center mb-4">
            <BarChart3 className="h-16 w-16 text-blue-600" />
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Access Request
          </h1>
          <div className="flex items-center justify-center space-x-2 text-sm text-gray-600 mb-4">
            <Building2 className="h-4 w-4" />
            <span>Internal Analytics Platform</span>
          </div>
        </div>

        <Card className="w-full shadow-lg border-0 bg-white/95 backdrop-blur-sm">
          <CardHeader className="text-center">
            <CardTitle className="text-xl font-bold text-gray-900">Account Access Required</CardTitle>
            <CardDescription className="text-gray-600">
              This system is for authorized company personnel only
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="text-center text-sm text-gray-600">
              <p className="mb-4">
                To gain access to the Internal Analytics Platform, please contact your system administrator or IT support team.
              </p>

              <div className="space-y-3 text-left bg-gray-50 p-4 rounded-lg">
                <div className="flex items-center space-x-2">
                  <Mail className="h-4 w-4 text-blue-600" />
                  <span className="font-medium">Email:</span>
                  <span><EMAIL></span>
                </div>
                <div className="flex items-center space-x-2">
                  <Phone className="h-4 w-4 text-blue-600" />
                  <span className="font-medium">Phone:</span>
                  <span>ext. 1234</span>
                </div>
              </div>
            </div>

            <div className="space-y-3">
              <Link href="/auth/login" className="w-full">
                <Button className="w-full bg-blue-600 hover:bg-blue-700">
                  Back to Login
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>

        <div className="text-center">
          <p className="text-xs text-gray-500">
            All access requests are subject to approval and company security policies.
          </p>
        </div>
      </div>
    </div>
  )
}
