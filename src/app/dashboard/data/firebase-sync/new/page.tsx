'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { useAuthState } from 'react-firebase-hooks/auth'
import { auth } from '@/lib/firebase'
import { Button } from '@/components/ui/button'

import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'

import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import {
  ArrowLeft,
  AlertCircle,
  Database,
  Plus,
  X
} from 'lucide-react'
import Link from 'next/link'
import { CreateExternalFirebaseConnection } from '@/lib/database.types'
import { externalFirebaseConnectionsService, dataSourcesService } from '@/lib/firestore'
import { multiFirebaseManager } from '@/lib/multi-firebase'
import { firebaseSyncSecurity } from '@/lib/firebase-sync-security'

interface FormData {
  name: string
  description: string
  projectId: string
  apiKey: string
  authDomain: string
  storageBucket: string
  messagingSenderId: string
  appId: string
  measurementId: string
  syncDirection: 'pull' | 'push' | 'bidirectional'
  syncFrequency: 'realtime' | 'hourly' | 'daily' | 'manual'
  conflictResolution: 'source_wins' | 'target_wins' | 'merge' | 'manual'
  collections: string[]
}

export default function NewFirebaseConnectionPage() {
  const router = useRouter()
  const [user] = useAuthState(auth)
  const [formData, setFormData] = useState<FormData>({
    name: '',
    description: '',
    projectId: '',
    apiKey: '',
    authDomain: '',
    storageBucket: '',
    messagingSenderId: '',
    appId: '',
    measurementId: '',
    syncDirection: 'pull',
    syncFrequency: 'realtime',
    conflictResolution: 'source_wins',
    collections: []
  })
  const [newCollection, setNewCollection] = useState('')
  const [saving, setSaving] = useState(false)
  const [error, setError] = useState('')
  const [warnings, setWarnings] = useState<string[]>([])
  const [validationErrors, setValidationErrors] = useState<string[]>([])

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const addCollection = () => {
    if (newCollection.trim() && !formData.collections.includes(newCollection.trim())) {
      setFormData(prev => ({
        ...prev,
        collections: [...prev.collections, newCollection.trim()]
      }))
      setNewCollection('')
    }
  }

  const removeCollection = (collection: string) => {
    setFormData(prev => ({
      ...prev,
      collections: prev.collections.filter(c => c !== collection)
    }))
  }

  const isFormValid = () => {
    const requiredFields = [
      'name',
      'projectId',
      'apiKey',
      'authDomain',
      'storageBucket',
      'messagingSenderId',
      'appId'
    ]

    return requiredFields.every(field => formData[field as keyof FormData]?.toString().trim()) &&
           formData.collections.length > 0
  }



  const saveConnection = async () => {
    if (!user) return

    setSaving(true)
    setError('')
    setWarnings([])
    setValidationErrors([])

    try {
      // Create connection data
      const connectionData: CreateExternalFirebaseConnection = {
        user_id: user.uid,
        name: formData.name,
        description: formData.description,
        project_id: formData.projectId,
        config: {
          apiKey: formData.apiKey,
          authDomain: formData.authDomain,
          projectId: formData.projectId,
          storageBucket: formData.storageBucket,
          messagingSenderId: formData.messagingSenderId,
          appId: formData.appId,
          measurementId: formData.measurementId
        },
        status: 'active',
        sync_settings: {
          enabled: true,
          collections: formData.collections,
          sync_direction: formData.syncDirection,
          conflict_resolution: formData.conflictResolution,
          sync_frequency: formData.syncFrequency
        }
      }

      // Validate connection data with security checks
      const validation = await firebaseSyncSecurity.validateConnectionData(user, connectionData, 'create')

      if (!validation.valid) {
        setValidationErrors(validation.errors)
        throw new Error(`Validation failed: ${validation.errors.join(', ')}`)
      }

      // Set warnings if any
      if (validation.warnings.length > 0) {
        setWarnings(validation.warnings)
      }

      // Sanitize data
      const sanitizedData = firebaseSyncSecurity.sanitizeConnectionData(connectionData)

      // Save to Firestore
      const connectionId = await externalFirebaseConnectionsService.create(sanitizedData)

      // Add to multi-firebase manager
      await multiFirebaseManager.addConnection({
        id: connectionId,
        ...sanitizedData,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })

      // Create corresponding DataSource entry for "My Files" integration
      const dataSourceConfig = {
        connection_id: connectionId,
        project_id: sanitizedData.project_id,
        collections: sanitizedData.sync_settings.collections,
        sync_direction: sanitizedData.sync_settings.sync_direction,
        sync_frequency: sanitizedData.sync_settings.sync_frequency,
        firebase_config: {
          authDomain: sanitizedData.config.authDomain,
          storageBucket: sanitizedData.config.storageBucket
          // Note: We don't store sensitive keys in the data source config
        },
        record_count: 0, // Will be updated when sync runs
        last_sync_at: null,
        schema: null // Will be populated after first sync
      }

      await dataSourcesService.create({
        user_id: user.uid,
        name: `Firebase: ${sanitizedData.name}`,
        type: 'firebase_sync',
        config: dataSourceConfig,
        status: 'active',
        last_sync: new Date().toISOString(),
        metadata: {
          source_type: 'firebase_connection',
          connection_id: connectionId,
          project_id: sanitizedData.project_id,
          collections_count: sanitizedData.sync_settings.collections.length
        }
      })

      console.log(`✅ Created Firebase connection and corresponding data source for: ${sanitizedData.name}`)

      // Redirect to connections page
      router.push('/dashboard/data/firebase-sync')

    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to save connection')
    } finally {
      setSaving(false)
    }
  }

  return (
    <div className="min-h-screen bg-slate-50">
      <div className="container mx-auto max-w-4xl px-6 py-8">
        {/* Header */}
        <div className="flex items-center space-x-4 mb-8">
          <Link href="/dashboard/data/firebase-sync">
            <Button variant="ghost" size="sm" className="text-gray-600 hover:text-gray-900 hover:bg-gray-100">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Connections
            </Button>
          </Link>
        </div>

        {/* Hero Section */}
        <div className="relative overflow-hidden rounded-3xl bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900 p-8 mb-8">
          <div className="absolute inset-0 opacity-20">
            <div className="absolute inset-0" style={{
              backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%239C92AC' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='4'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
            }}></div>
          </div>
          <div className="relative z-10">
            <h1 className="text-4xl md:text-5xl font-bold text-white mb-4 leading-tight">
              Add Firebase Connection
            </h1>
            <p className="text-xl text-gray-300 leading-relaxed">
              Connect to another Firebase project for real-time data synchronization
            </p>
          </div>
        </div>

        {/* Form */}
        <div className="space-y-6">
          {/* Basic Information */}
          <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300">
            <div className="mb-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Basic Information</h3>
              <p className="text-sm text-gray-600">
                Provide basic details about this connection
              </p>
            </div>
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="name" className="text-gray-700">Connection Name *</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    placeholder="e.g., Production Database"
                    className="bg-white border-gray-200 text-gray-900 placeholder:text-gray-500"
                  />
                </div>
                <div>
                  <Label htmlFor="projectId" className="text-gray-700">Firebase Project ID *</Label>
                  <Input
                    id="projectId"
                    value={formData.projectId}
                    onChange={(e) => handleInputChange('projectId', e.target.value)}
                    placeholder="your-project-id"
                    className="bg-white border-gray-200 text-gray-900 placeholder:text-gray-500"
                  />
                </div>
              </div>
              <div>
                <Label htmlFor="description" className="text-gray-700">Description</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  placeholder="Optional description of this connection"
                  className="bg-white border-gray-200 text-gray-900 placeholder:text-gray-500"
                />
              </div>
            </div>
          </div>

          {/* Firebase Configuration */}
          <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300">
            <div className="mb-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Firebase Configuration</h3>
              <p className="text-sm text-gray-600">
                Enter the Firebase configuration from your project settings
              </p>
            </div>
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="apiKey" className="text-gray-700">API Key *</Label>
                  <Input
                    id="apiKey"
                    type="password"
                    value={formData.apiKey}
                    onChange={(e) => handleInputChange('apiKey', e.target.value)}
                    placeholder="AIza..."
                    className="bg-white border-gray-200 text-gray-900 placeholder:text-gray-500"
                  />
                </div>
                <div>
                  <Label htmlFor="authDomain" className="text-gray-700">Auth Domain *</Label>
                  <Input
                    id="authDomain"
                    value={formData.authDomain}
                    onChange={(e) => handleInputChange('authDomain', e.target.value)}
                    placeholder="your-project.firebaseapp.com"
                    className="bg-white border-gray-200 text-gray-900 placeholder:text-gray-500"
                  />
                </div>
                <div>
                  <Label htmlFor="storageBucket" className="text-gray-700">Storage Bucket *</Label>
                  <Input
                    id="storageBucket"
                    value={formData.storageBucket}
                    onChange={(e) => handleInputChange('storageBucket', e.target.value)}
                    placeholder="your-project.appspot.com"
                    className="bg-white border-gray-200 text-gray-900 placeholder:text-gray-500"
                  />
                </div>
                <div>
                  <Label htmlFor="messagingSenderId" className="text-gray-700">Messaging Sender ID *</Label>
                  <Input
                    id="messagingSenderId"
                    value={formData.messagingSenderId}
                    onChange={(e) => handleInputChange('messagingSenderId', e.target.value)}
                    placeholder="123456789"
                    className="bg-white border-gray-200 text-gray-900 placeholder:text-gray-500"
                  />
                </div>
                <div>
                  <Label htmlFor="appId" className="text-gray-700">App ID *</Label>
                  <Input
                    id="appId"
                    value={formData.appId}
                    onChange={(e) => handleInputChange('appId', e.target.value)}
                    placeholder="1:123456789:web:abc123"
                    className="bg-white border-gray-200 text-gray-900 placeholder:text-gray-500"
                  />
                </div>
                <div>
                  <Label htmlFor="measurementId" className="text-gray-700">Measurement ID</Label>
                  <Input
                    id="measurementId"
                    value={formData.measurementId}
                    onChange={(e) => handleInputChange('measurementId', e.target.value)}
                    placeholder="G-XXXXXXXXXX"
                    className="bg-white border-gray-200 text-gray-900 placeholder:text-gray-500"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Sync Settings */}
          <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300">
            <div className="mb-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Sync Settings</h3>
              <p className="text-sm text-gray-600">
                Configure how data should be synchronized between projects
              </p>
            </div>
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <Label className="text-gray-700">Sync Direction</Label>
                  <Select value={formData.syncDirection} onValueChange={(value: string) => handleInputChange('syncDirection', value)}>
                    <SelectTrigger className="bg-white border-gray-200 text-gray-900">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="pull">Pull (External → Local)</SelectItem>
                      <SelectItem value="push">Push (Local → External)</SelectItem>
                      <SelectItem value="bidirectional">Bidirectional</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label className="text-gray-700">Sync Frequency</Label>
                  <Select value={formData.syncFrequency} onValueChange={(value: string) => handleInputChange('syncFrequency', value)}>
                    <SelectTrigger className="bg-white border-gray-200 text-gray-900">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="realtime">Real-time</SelectItem>
                      <SelectItem value="hourly">Hourly</SelectItem>
                      <SelectItem value="daily">Daily</SelectItem>
                      <SelectItem value="manual">Manual</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label className="text-gray-700">Conflict Resolution</Label>
                  <Select value={formData.conflictResolution} onValueChange={(value: string) => handleInputChange('conflictResolution', value)}>
                    <SelectTrigger className="bg-white border-gray-200 text-gray-900">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="source_wins">Source Wins</SelectItem>
                      <SelectItem value="target_wins">Target Wins</SelectItem>
                      <SelectItem value="merge">Merge</SelectItem>
                      <SelectItem value="manual">Manual</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Collections */}
              <div>
                <Label className="text-gray-700">Collections to Sync *</Label>
                <div className="flex space-x-2 mt-2">
                  <Input
                    value={newCollection}
                    onChange={(e) => setNewCollection(e.target.value)}
                    placeholder="Enter collection name"
                    className="bg-white border-gray-200 text-gray-900 placeholder:text-gray-500"
                    onKeyPress={(e) => e.key === 'Enter' && addCollection()}
                  />
                  <Button onClick={addCollection} size="sm" variant="outline" className="border-gray-200 text-gray-700 hover:bg-gray-50">
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>
                <div className="flex flex-wrap gap-2 mt-3">
                  {formData.collections.map((collection) => (
                    <Badge key={collection} variant="secondary" className="bg-purple-100 text-purple-700 hover:bg-purple-200">
                      {collection}
                      <button
                        onClick={() => removeCollection(collection)}
                        className="ml-2 hover:text-red-600"
                      >
                        <X className="h-3 w-3" />
                      </button>
                    </Badge>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Error Messages */}
          {validationErrors.length > 0 && (
            <Alert className="border-red-500/50 bg-red-50 border-red-200">
              <AlertCircle className="h-4 w-4 text-red-500" />
              <AlertDescription className="text-red-700">
                <div className="font-medium mb-2">Validation Errors:</div>
                <ul className="list-disc list-inside space-y-1">
                  {validationErrors.map((error, index) => (
                    <li key={index}>{error}</li>
                  ))}
                </ul>
              </AlertDescription>
            </Alert>
          )}

          {/* Warnings */}
          {warnings.length > 0 && (
            <Alert className="border-yellow-500/50 bg-yellow-50 border-yellow-200">
              <AlertCircle className="h-4 w-4 text-yellow-500" />
              <AlertDescription className="text-yellow-700">
                <div className="font-medium mb-2">Warnings:</div>
                <ul className="list-disc list-inside space-y-1">
                  {warnings.map((warning, index) => (
                    <li key={index}>{warning}</li>
                  ))}
                </ul>
              </AlertDescription>
            </Alert>
          )}

          {/* General Error */}
          {error && (
            <Alert className="border-red-500/50 bg-red-50 border-red-200">
              <AlertCircle className="h-4 w-4 text-red-500" />
              <AlertDescription className="text-red-700">{error}</AlertDescription>
            </Alert>
          )}

          {/* Actions */}
          <div className="flex items-center justify-between">
            <Link href="/dashboard/data/firebase-sync">
              <Button variant="ghost" className="text-gray-600 hover:text-gray-900 hover:bg-gray-100">
                Cancel
              </Button>
            </Link>
            <Button
              onClick={saveConnection}
              disabled={saving || !isFormValid()}
              className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white font-semibold disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {saving ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
              ) : (
                <Database className="h-4 w-4 mr-2" />
              )}
              Create Connection
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
