'use client'

import { useState, useEffect } from 'react'
import { useAuthState } from 'react-firebase-hooks/auth'
import { auth } from '@/lib/firebase'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Modal } from '@/components/ui/modal'
import {
  Plus,
  Database,
  Settings,
  Play,
  Pause,
  Trash2,
  AlertCircle,
  CheckCircle,
  Clock,
  ArrowRight,
  ArrowLeft,
  ArrowLeftRight,
  Eye,
  Edit,
  TestTube,
  X
} from 'lucide-react'
import Link from 'next/link'
import { ExternalFirebaseConnection } from '@/lib/database.types'
import { getUserExternalConnections, dataSourcesService, getUserDataSources } from '@/lib/firestore'
import { multiFirebaseManager } from '@/lib/multi-firebase'
import { firebaseSyncEngine, type SyncRule } from '@/lib/firebase-sync'
import { SyncDashboard } from '@/components/firebase-sync/sync-dashboard'
import { useToast } from '@/components/ui/toast'

interface ConnectionWithStatus extends ExternalFirebaseConnection {
  connectionStatus: 'connected' | 'disconnected' | 'error' | 'not_found'
  syncStatus: {
    isActive: boolean
    lastSync?: Date
    stats?: {
      documentsProcessed: number
      documentsSuccess: number
      documentsFailed: number
      errors: string[]
    }
  }
}

export default function FirebaseSyncPage() {
  const [user] = useAuthState(auth)
  const [connections, setConnections] = useState<ConnectionWithStatus[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedConnection, setSelectedConnection] = useState<string | null>(null)
  const [viewModalOpen, setViewModalOpen] = useState(false)
  const [editModalOpen, setEditModalOpen] = useState(false)
  const [selectedConnectionForModal, setSelectedConnectionForModal] = useState<ConnectionWithStatus | null>(null)
  const { toast } = useToast()

  useEffect(() => {
    if (user) {
      loadConnections()
    }
  }, [user])

  // Create DataSource for existing Firebase connections that don't have one
  const ensureDataSourceExists = async (connection: ExternalFirebaseConnection) => {
    try {
      if (!user) return

      // Check if DataSource already exists
      const userDataSources = await getUserDataSources(user.uid)
      const existingDataSource = userDataSources.find(ds =>
        ds.type === 'firebase_sync' &&
        ds.metadata?.connection_id === connection.id
      )

      if (existingDataSource) {
        console.log(`✅ DataSource already exists for connection ${connection.name}`)
        return
      }

      console.log(`🔧 Creating missing DataSource for connection ${connection.name}`)

      // Create DataSource for this connection
      const dataSourceConfig = {
        connection_id: connection.id,
        project_id: connection.project_id,
        collections: connection.sync_settings.collections,
        sync_direction: connection.sync_settings.sync_direction,
        sync_frequency: connection.sync_settings.sync_frequency,
        firebase_config: {
          authDomain: connection.config.authDomain,
          storageBucket: connection.config.storageBucket
        },
        record_count: 0, // Will be updated when sync runs
        last_sync_at: null,
        schema: null // Will be populated after first sync
      }

      await dataSourcesService.create({
        user_id: user.uid,
        name: `Firebase: ${connection.name}`,
        type: 'firebase_sync',
        config: dataSourceConfig,
        status: 'active',
        last_sync: new Date().toISOString(),
        metadata: {
          source_type: 'firebase_connection',
          connection_id: connection.id,
          project_id: connection.project_id,
          collections_count: connection.sync_settings.collections.length
        }
      })

      console.log(`✅ Created DataSource for existing connection: ${connection.name}`)

    } catch (error) {
      console.error(`Failed to create DataSource for connection ${connection.name}:`, error)
    }
  }

  const loadConnections = async () => {
    if (!user) return

    try {
      setLoading(true)
      console.log(`📋 Loading connections for user: ${user.uid}`)

      const userConnections = await getUserExternalConnections(user.uid)
      console.log(`📊 Found ${userConnections.length} connections:`, userConnections.map(c => ({
        id: c.id,
        name: c.name,
        project_id: c.project_id,
        status: c.status,
        collections: c.sync_settings?.collections || []
      })))

      // Ensure all connections are added to the multi-firebase manager
      for (const connection of userConnections) {
        const status = multiFirebaseManager.getConnectionStatus(connection.id)
        console.log(`🔍 Connection ${connection.name} (${connection.id}) status: ${status}`)

        // Ensure DataSource exists for this connection
        await ensureDataSourceExists(connection)

        if (status === 'not_found') {
          console.log(`➕ Adding connection ${connection.name} to multi-firebase manager...`)
          const addResult = await multiFirebaseManager.addConnection(connection)
          console.log(`📝 Add connection result for ${connection.name}:`, addResult)

          // Check status again after adding
          const newStatus = multiFirebaseManager.getConnectionStatus(connection.id)
          console.log(`🔄 New status for ${connection.name}: ${newStatus}`)
        }
      }

      // Add status information to each connection
      const connectionsWithStatus: ConnectionWithStatus[] = userConnections.map(conn => ({
        ...conn,
        connectionStatus: multiFirebaseManager.getConnectionStatus(conn.id),
        syncStatus: firebaseSyncEngine.getSyncStatus(conn.id)
      }))

      setConnections(connectionsWithStatus)
    } catch (error) {
      console.error('Failed to load connections:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleStartSync = async (connection: ConnectionWithStatus) => {
    try {
      console.log(`🚀 Attempting to start sync for connection: ${connection.name}`)
      console.log('Connection details:', {
        id: connection.id,
        name: connection.name,
        project_id: connection.project_id,
        collections: connection.sync_settings.collections,
        direction: connection.sync_settings.sync_direction,
        connectionStatus: connection.connectionStatus
      })

      // Check if connection is properly established
      if (connection.connectionStatus !== 'connected') {
        console.error(`❌ Connection ${connection.name} is not connected. Status: ${connection.connectionStatus}`)
        alert(`Connection "${connection.name}" is not connected. Please check your connection settings.`)
        return
      }

      // Validate collections
      if (!connection.sync_settings.collections || connection.sync_settings.collections.length === 0) {
        console.error(`❌ No collections configured for sync in connection: ${connection.name}`)
        alert(`No collections configured for sync in connection "${connection.name}". Please edit the connection to add collections.`)
        return
      }

      // Create sync rules from connection settings
      const syncRules: SyncRule[] = connection.sync_settings.collections.map(collection => ({
        sourceCollection: collection,
        targetCollection: collection, // Same collection name in local DB
        fieldMappings: {}, // No field transformations by default
        filters: [] // No filters by default
      }))

      console.log('Generated sync rules:', syncRules)

      // Start the sync
      const success = await firebaseSyncEngine.startSync(
        connection.id,
        syncRules,
        connection.sync_settings.sync_direction
      )

      if (success) {
        console.log(`✅ Successfully started sync for connection: ${connection.name}`)
        // Reload connections to update status
        await loadConnections()

        toast({
          type: 'success',
          title: 'Sync Started Successfully!',
          description: `Data synchronization has been initiated for "${connection.name}". You'll see updates in real-time.`,
          duration: 6000,
          action: {
            label: 'View Dashboard',
            onClick: () => {
              // Could navigate to dashboard or show sync status
              console.log('Navigate to dashboard')
            }
          }
        })
      } else {
        console.error(`❌ Failed to start sync for connection: ${connection.name}`)
        toast({
          type: 'error',
          title: 'Sync Failed to Start',
          description: `Unable to start synchronization for "${connection.name}". Please check your connection settings and try again.`,
          duration: 8000
        })
      }
    } catch (error) {
      console.error('Error starting sync:', error)
      toast({
        type: 'error',
        title: 'Sync Error',
        description: error instanceof Error ? error.message : 'An unexpected error occurred while starting the sync.',
        duration: 8000
      })
    }
  }

  const handleStopSync = async (connection: ConnectionWithStatus) => {
    try {
      console.log(`🛑 Stopping sync for connection: ${connection.name}`)

      const success = await firebaseSyncEngine.stopSync(connection.id)

      if (success) {
        console.log(`✅ Successfully stopped sync for connection: ${connection.name}`)
        // Reload connections to update status
        await loadConnections()

        toast({
          type: 'info',
          title: 'Sync Paused',
          description: `Data synchronization has been paused for "${connection.name}". You can resume it anytime.`,
          duration: 4000
        })
      } else {
        console.error(`❌ Failed to stop sync for connection: ${connection.name}`)
        toast({
          type: 'error',
          title: 'Failed to Pause Sync',
          description: `Unable to pause synchronization for "${connection.name}". Please try again.`,
          duration: 6000
        })
      }
    } catch (error) {
      console.error('Error stopping sync:', error)
      toast({
        type: 'error',
        title: 'Sync Error',
        description: error instanceof Error ? error.message : 'An unexpected error occurred while pausing the sync.',
        duration: 6000
      })
    }
  }

  const handleViewConnection = (connection: ConnectionWithStatus) => {
    setSelectedConnectionForModal(connection)
    setViewModalOpen(true)
  }

  const handleEditConnection = (connection: ConnectionWithStatus) => {
    setSelectedConnectionForModal(connection)
    setEditModalOpen(true)
  }

  const closeModals = () => {
    setViewModalOpen(false)
    setEditModalOpen(false)
    setSelectedConnectionForModal(null)
  }

  const getStatusBadge = (status: string) => {
    const styles = {
      connected: 'bg-green-100 text-green-800',
      disconnected: 'bg-gray-100 text-gray-800',
      error: 'bg-red-100 text-red-800',
      not_found: 'bg-yellow-100 text-yellow-800'
    }
    const labels = {
      connected: 'Connected',
      disconnected: 'Disconnected',
      error: 'Error',
      not_found: 'Not Found'
    }
    return (
      <Badge className={`${styles[status as keyof typeof styles]} hover:${styles[status as keyof typeof styles]}`}>
        {labels[status as keyof typeof labels]}
      </Badge>
    )
  }

  const getSyncDirectionIcon = (direction: string) => {
    switch (direction) {
      case 'pull':
        return <ArrowLeft className="h-4 w-4" />
      case 'push':
        return <ArrowRight className="h-4 w-4" />
      case 'bidirectional':
        return <ArrowLeftRight className="h-4 w-4" />
      default:
        return <Database className="h-4 w-4" />
    }
  }

  const getSyncStatusBadge = (isActive: boolean) => {
    return (
      <Badge className={`${isActive ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'} hover:${isActive ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}`}>
        {isActive ? (
          <>
            <Play className="h-3 w-3 mr-1" />
            Active
          </>
        ) : (
          <>
            <Pause className="h-3 w-3 mr-1" />
            Inactive
          </>
        )}
      </Badge>
    )
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 p-6">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-slate-50">
      <div className="container mx-auto max-w-7xl px-6 py-8">
        {/* Hero Section */}
        <div className="relative overflow-hidden rounded-3xl bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900 p-8 mb-8">
          <div className="absolute inset-0 opacity-20">
            <div className="absolute inset-0" style={{
              backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%239C92AC' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='4'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
            }}></div>
          </div>
          <div className="relative z-10">
            <div className="max-w-2xl">
              <div className="flex items-center space-x-2 mb-4">
                <div className="flex items-center space-x-1">
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                  <span className="text-green-400 text-sm font-medium">Firebase Sync Ready</span>
                </div>
              </div>
              <h1 className="text-4xl md:text-5xl font-bold text-white mb-4 leading-tight">
                Real-time Firebase Sync,
                <span className="block text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-purple-400">
                  Seamlessly
                </span>
              </h1>
              <p className="text-xl text-gray-300 mb-8 leading-relaxed">
                Connect and synchronize data with other Firebase projects in real-time
              </p>
              <Link href="/dashboard/data/firebase-sync/new">
                <Button className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white font-semibold shadow-lg hover:shadow-xl transition-all duration-300">
                  <Plus className="mr-2 h-4 w-4" />
                  Add Connection
                </Button>
              </Link>
            </div>
          </div>
        </div>

        {/* Content */}
        <Tabs defaultValue="dashboard" className="space-y-6">
          <TabsList className="bg-white rounded-xl p-1 shadow-sm border border-gray-200">
            <TabsTrigger
              value="dashboard"
              className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-purple-600 data-[state=active]:to-pink-600 data-[state=active]:text-white data-[state=active]:shadow-md transition-all duration-300"
            >
              Dashboard
            </TabsTrigger>
            <TabsTrigger
              value="connections"
              className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-purple-600 data-[state=active]:to-pink-600 data-[state=active]:text-white data-[state=active]:shadow-md transition-all duration-300"
            >
              Connections
            </TabsTrigger>
            <TabsTrigger
              value="sync-logs"
              className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-purple-600 data-[state=active]:to-pink-600 data-[state=active]:text-white data-[state=active]:shadow-md transition-all duration-300"
            >
              Sync Logs
            </TabsTrigger>
          </TabsList>

          <TabsContent value="dashboard" className="space-y-6">
            <SyncDashboard connections={connections} userId={user?.uid || ''} />
          </TabsContent>

          <TabsContent value="connections" className="space-y-6">
            {connections.length === 0 ? (
              <div className="bg-white rounded-2xl p-12 shadow-sm border border-gray-100 text-center">
                <Database className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-gray-900 mb-2">No Firebase Connections</h3>
                <p className="text-gray-600 mb-6">
                  Connect to other Firebase projects to start syncing data in real-time
                </p>
                <Link href="/dashboard/data/firebase-sync/new">
                  <Button className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white font-semibold shadow-lg hover:shadow-xl transition-all duration-300">
                    <Plus className="mr-2 h-4 w-4" />
                    Add Your First Connection
                  </Button>
                </Link>
              </div>
            ) : (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {connections.map((connection) => (
                  <div key={connection.id} className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex-1">
                        <h3 className="text-lg font-semibold text-gray-900">{connection.name}</h3>
                        <p className="text-sm text-gray-600 mt-1">
                          {connection.description || connection.project_id}
                        </p>
                      </div>
                      <div className="flex items-center space-x-2">
                        {getStatusBadge(connection.connectionStatus)}
                        {getSyncStatusBadge(connection.syncStatus.isActive)}
                      </div>
                    </div>
                    <div className="space-y-4">
                      {/* Sync Settings */}
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-gray-600">Sync Direction:</span>
                        <div className="flex items-center space-x-1 text-gray-900">
                          {getSyncDirectionIcon(connection.sync_settings.sync_direction)}
                          <span className="capitalize">{connection.sync_settings.sync_direction}</span>
                        </div>
                      </div>

                      <div className="flex items-center justify-between text-sm">
                        <span className="text-gray-600">Collections:</span>
                        <span className="text-gray-900">{connection.sync_settings.collections.length} collections</span>
                      </div>

                      <div className="flex items-center justify-between text-sm">
                        <span className="text-gray-600">Frequency:</span>
                        <span className="text-gray-900 capitalize">{connection.sync_settings.sync_frequency}</span>
                      </div>

                      {/* Sync Stats */}
                      {connection.syncStatus.stats && (
                        <div className="pt-4 border-t border-gray-200">
                          <div className="grid grid-cols-3 gap-4 text-center">
                            <div>
                              <div className="text-lg font-semibold text-gray-900">
                                {connection.syncStatus.stats.documentsProcessed}
                              </div>
                              <div className="text-xs text-gray-500">Processed</div>
                            </div>
                            <div>
                              <div className="text-lg font-semibold text-green-600">
                                {connection.syncStatus.stats.documentsSuccess}
                              </div>
                              <div className="text-xs text-gray-500">Success</div>
                            </div>
                            <div>
                              <div className="text-lg font-semibold text-red-600">
                                {connection.syncStatus.stats.documentsFailed}
                              </div>
                              <div className="text-xs text-gray-500">Failed</div>
                            </div>
                          </div>
                        </div>
                      )}

                      {/* Actions */}
                      <div className="flex items-center justify-between pt-4">
                        <div className="flex items-center space-x-2">
                          <Button
                            size="sm"
                            variant="ghost"
                            className="text-gray-600 hover:text-gray-900 hover:bg-gray-100"
                            onClick={() => handleViewConnection(connection)}
                          >
                            <Eye className="h-4 w-4 mr-1" />
                            View
                          </Button>
                          <Button
                            size="sm"
                            variant="ghost"
                            className="text-gray-600 hover:text-gray-900 hover:bg-gray-100"
                            onClick={() => handleEditConnection(connection)}
                          >
                            <Edit className="h-4 w-4 mr-1" />
                            Edit
                          </Button>
                        </div>
                        <div className="flex items-center space-x-2">
                          {connection.syncStatus.isActive ? (
                            <Button
                              size="sm"
                              variant="ghost"
                              className="text-yellow-600 hover:text-yellow-700 hover:bg-yellow-50"
                              onClick={() => handleStopSync(connection)}
                            >
                              <Pause className="h-4 w-4 mr-1" />
                              Pause
                            </Button>
                          ) : (
                            <Button
                              size="sm"
                              variant="ghost"
                              className="text-green-600 hover:text-green-700 hover:bg-green-50"
                              onClick={() => handleStartSync(connection)}
                            >
                              <Play className="h-4 w-4 mr-1" />
                              Start
                            </Button>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </TabsContent>

          <TabsContent value="sync-logs" className="space-y-6">
            <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
              <div className="mb-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Sync Logs</h3>
                <p className="text-sm text-gray-600">
                  View recent synchronization activity and logs
                </p>
              </div>
              <div className="text-center py-8">
                <Clock className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600">Sync logs will appear here once you start syncing data</p>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </div>

      {/* View Connection Modal */}
      <Modal
        isOpen={viewModalOpen}
        onClose={closeModals}
        title="Connection Details"
        size="lg"
      >
        {selectedConnectionForModal && (
          <div className="space-y-6">
            {/* Basic Info */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Name</label>
                <p className="text-gray-900">{selectedConnectionForModal.name}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Project ID</label>
                <p className="text-gray-900">{selectedConnectionForModal.project_id}</p>
              </div>
            </div>

            {selectedConnectionForModal.description && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
                <p className="text-gray-900">{selectedConnectionForModal.description}</p>
              </div>
            )}

            {/* Status */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Connection Status</label>
                <div className="flex items-center space-x-2">
                  {getStatusBadge(selectedConnectionForModal.connectionStatus)}
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Sync Status</label>
                <div className="flex items-center space-x-2">
                  {getSyncStatusBadge(selectedConnectionForModal.syncStatus.isActive)}
                </div>
              </div>
            </div>

            {/* Sync Settings */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900">Sync Settings</h3>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Direction</label>
                  <div className="flex items-center space-x-2">
                    {selectedConnectionForModal.sync_settings.sync_direction === 'pull' && <ArrowLeft className="h-4 w-4 text-blue-500" />}
                    {selectedConnectionForModal.sync_settings.sync_direction === 'push' && <ArrowRight className="h-4 w-4 text-green-500" />}
                    {selectedConnectionForModal.sync_settings.sync_direction === 'bidirectional' && <ArrowLeftRight className="h-4 w-4 text-purple-500" />}
                    <span className="capitalize">{selectedConnectionForModal.sync_settings.sync_direction}</span>
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Frequency</label>
                  <p className="text-gray-900 capitalize">{selectedConnectionForModal.sync_settings.sync_frequency}</p>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Collections ({selectedConnectionForModal.sync_settings.collections.length})</label>
                <div className="flex flex-wrap gap-2 mt-2">
                  {selectedConnectionForModal.sync_settings.collections.map((collection) => (
                    <Badge key={collection} variant="secondary" className="bg-purple-100 text-purple-800">
                      {collection}
                    </Badge>
                  ))}
                </div>
              </div>
            </div>

            {/* Firebase Config (masked) */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900">Firebase Configuration</h3>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Auth Domain</label>
                  <p className="text-gray-900 font-mono text-xs">{selectedConnectionForModal.config.authDomain}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Storage Bucket</label>
                  <p className="text-gray-900 font-mono text-xs">{selectedConnectionForModal.config.storageBucket}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">API Key</label>
                  <p className="text-gray-900 font-mono text-xs">{selectedConnectionForModal.config.apiKey.substring(0, 10)}...</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">App ID</label>
                  <p className="text-gray-900 font-mono text-xs">{selectedConnectionForModal.config.appId.substring(0, 15)}...</p>
                </div>
              </div>
            </div>

            {/* Timestamps */}
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Created</label>
                <p className="text-gray-900">{new Date(selectedConnectionForModal.created_at).toLocaleString()}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Last Updated</label>
                <p className="text-gray-900">{new Date(selectedConnectionForModal.updated_at).toLocaleString()}</p>
              </div>
            </div>
          </div>
        )}
      </Modal>

      {/* Edit Connection Modal */}
      <Modal
        isOpen={editModalOpen}
        onClose={closeModals}
        title="Edit Connection"
        size="lg"
      >
        {selectedConnectionForModal && (
          <div className="space-y-6">
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-center space-x-2">
                <AlertCircle className="h-5 w-5 text-blue-600" />
                <p className="text-blue-800 font-medium">Edit Functionality Coming Soon</p>
              </div>
              <p className="text-blue-700 text-sm mt-2">
                Connection editing is currently under development. For now, you can view connection details and create new connections.
              </p>
            </div>

            {/* Basic connection info for reference */}
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Connection Name</label>
                <p className="text-gray-900 font-medium">{selectedConnectionForModal.name}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Project ID</label>
                <p className="text-gray-900">{selectedConnectionForModal.project_id}</p>
              </div>
            </div>

            <div className="flex justify-end space-x-3">
              <Button variant="outline" onClick={closeModals}>
                Close
              </Button>
              <Link href="/dashboard/data/firebase-sync/new">
                <Button className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white">
                  <Plus className="h-4 w-4 mr-2" />
                  Create New Connection
                </Button>
              </Link>
            </div>
          </div>
        )}
      </Modal>
    </div>
  )
}
