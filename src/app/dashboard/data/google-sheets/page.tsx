'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { GoogleSheetsConnect } from '@/components/data/google-sheets-connect'
import { GoogleSheetsPreview } from '@/components/data/google-sheets-preview'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Progress } from '@/components/ui/progress'
import { 
  ArrowLeft, 
  FileSpreadsheet, 
  CheckCircle, 
  AlertCircle,
  Loader2,
  Sparkles
} from 'lucide-react'
import { dataSourcesService } from '@/lib/firestore'
import { useAuth } from '@/components/auth/auth-provider'
import { googleSheetsService } from '@/lib/google-sheets'

interface GoogleSheet {
  id: string
  name: string
  url: string
  lastModified: string
  sheets: Array<{
    id: number
    title: string
    rowCount: number
    columnCount: number
  }>
}

interface ProcessingStep {
  id: string
  name: string
  status: 'pending' | 'processing' | 'completed' | 'error'
  progress: number
}

export default function GoogleSheetsImportPage() {
  const [selectedSheet, setSelectedSheet] = useState<GoogleSheet | null>(null)
  const [selectedSheetTab, setSelectedSheetTab] = useState<string>('')
  const [importedData, setImportedData] = useState<Record<string, any>[]>([])
  const [dataSchema, setDataSchema] = useState<Record<string, string>>({})
  const [step, setStep] = useState<'connect' | 'preview' | 'import' | 'complete'>('connect')
  const [error, setError] = useState<string>('')
  const [isImporting, setIsImporting] = useState(false)
  const [processingSteps, setProcessingSteps] = useState<ProcessingStep[]>([
    { id: 'validate', name: 'Validating sheet data', status: 'pending', progress: 0 },
    { id: 'process', name: 'Processing data', status: 'pending', progress: 0 },
    { id: 'save', name: 'Saving data source', status: 'pending', progress: 0 }
  ])
  
  const router = useRouter()
  const { user } = useAuth()

  const updateStepStatus = (stepId: string, status: ProcessingStep['status'], progress: number) => {
    setProcessingSteps(prev => prev.map(step => 
      step.id === stepId ? { ...step, status, progress } : step
    ))
  }

  const handleSheetSelect = (sheet: GoogleSheet, sheetTab: string) => {
    setSelectedSheet(sheet)
    setSelectedSheetTab(sheetTab)
    setStep('preview')
    setError('')
  }

  const handleSheetTabChange = (tabName: string) => {
    setSelectedSheetTab(tabName)
    // Clear previous data when switching tabs
    setImportedData([])
    setDataSchema({})
  }

  const handleDataLoad = (data: Record<string, any>[], schema: Record<string, string>) => {
    setImportedData(data)
    setDataSchema(schema)
  }

  const handleImportData = async () => {
    if (!selectedSheet || !selectedSheetTab || !user) {
      setError('Missing required data for import')
      return
    }

    try {
      setIsImporting(true)
      setStep('import')
      setError('')

      // Step 1: Validate data
      updateStepStatus('validate', 'processing', 25)
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      if (importedData.length === 0) {
        throw new Error('No data to import')
      }
      
      updateStepStatus('validate', 'completed', 100)

      // Step 2: Process data
      updateStepStatus('process', 'processing', 50)
      await new Promise(resolve => setTimeout(resolve, 1500))
      
      // Here you would process and clean the data
      const processedData = importedData.map(row => {
        const processed: Record<string, any> = {}
        Object.entries(row).forEach(([key, value]) => {
          // Clean and validate data based on schema
          processed[key] = value
        })
        return processed
      })
      
      updateStepStatus('process', 'completed', 100)

      // Step 3: Save data source
      updateStepStatus('save', 'processing', 75)
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      const dataSourceConfig = {
        sheet_id: selectedSheet.id,
        sheet_name: selectedSheet.name,
        sheet_tab: selectedSheetTab,
        sheet_url: selectedSheet.url,
        schema: dataSchema,
        last_modified: selectedSheet.lastModified
      }

      const dataSourceId = await dataSourcesService.create({
        user_id: user.uid,
        name: `${selectedSheet.name} - ${selectedSheetTab}`,
        type: 'google_sheets',
        config: dataSourceConfig,
        status: 'active',
        last_sync: new Date().toISOString()
      })

      updateStepStatus('save', 'completed', 100)
      
      // Move to completion step
      setStep('complete')
      
    } catch (error) {
      console.error('Error importing data:', error)
      setError(error instanceof Error ? error.message : 'Failed to import data')
      
      // Mark current step as error
      const currentStep = processingSteps.find(step => step.status === 'processing')
      if (currentStep) {
        updateStepStatus(currentStep.id, 'error', 0)
      }
    } finally {
      setIsImporting(false)
    }
  }

  const handleError = (errorMessage: string) => {
    setError(errorMessage)
  }

  const getStepProgress = () => {
    const completedSteps = processingSteps.filter(step => step.status === 'completed').length
    return (completedSteps / processingSteps.length) * 100
  }

  return (
    <div className="min-h-screen bg-slate-50">
      <div className="container mx-auto max-w-7xl px-6 py-8">
        {/* Hero Section */}
        <div className="relative overflow-hidden rounded-3xl bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900 p-8 mb-8">
          <div className="absolute inset-0 opacity-20">
            <div className="absolute inset-0" style={{
              backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%239C92AC' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='4'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
            }}></div>
          </div>
          <div className="relative z-10">
            <div className="max-w-2xl">
              <div className="flex items-center space-x-1 mb-4">
                <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                <span className="text-green-400 text-sm font-medium">Google Sheets Ready</span>
              </div>
              <h1 className="text-4xl md:text-5xl font-bold text-white mb-4 leading-tight">
                Import from Google Sheets,
                <span className="block text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-purple-400">
                  Seamlessly
                </span>
              </h1>
              <p className="text-xl text-gray-300 mb-8 leading-relaxed">
                Connect your Google account and import data from your spreadsheets to create professional reports
              </p>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Content Area */}
          <div className="lg:col-span-1">
            {step === 'connect' && (
              <div className="bg-white rounded-2xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300">
                <GoogleSheetsConnect
                  onSheetSelect={handleSheetSelect}
                  onError={handleError}
                />
              </div>
            )}

            {(step === 'preview' || step === 'import' || step === 'complete') && selectedSheet && (
              <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300">
                {/* Back Button */}
                <div className="mb-6">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      setStep('connect')
                      setSelectedSheet(null)
                      setSelectedSheetTab('')
                      setImportedData([])
                      setDataSchema({})
                      setError('')
                    }}
                    className="mb-4 text-gray-600 hover:text-gray-900 hover:bg-gray-100 transition-all duration-200"
                  >
                    <ArrowLeft className="mr-2 h-4 w-4" />
                    Back to Sheet Selection
                  </Button>
                </div>

                <div className="mb-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">Selected Sheet</h3>
                  <p className="text-sm text-gray-600">
                    {selectedSheet.name} - {selectedSheetTab}
                  </p>
                </div>
                <div className="p-3 bg-gray-50 rounded-lg">
                  <p className="font-medium text-sm text-gray-900">{selectedSheet.name}</p>
                  <p className="text-xs text-gray-600">Tab: {selectedSheetTab}</p>
                  <p className="text-xs text-gray-600">
                    {importedData.length} rows • {Object.keys(dataSchema).length} columns
                  </p>
                </div>
              </div>
            )}

            {/* Processing Steps */}
            {(step === 'import' || step === 'complete') && (
              <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300 mt-6">
                <div className="mb-6">
                  <h3 className="text-lg font-semibold text-gray-900">Processing Status</h3>
                </div>
                <div className="space-y-4">
                  {processingSteps.map((step) => (
                    <div key={step.id} className="flex items-center space-x-3">
                      <div className={`w-6 h-6 rounded-full flex items-center justify-center ${
                        step.status === 'completed' ? 'bg-green-600' :
                        step.status === 'processing' ? 'bg-purple-600' :
                        step.status === 'error' ? 'bg-red-600' :
                        'bg-gray-300'
                      }`}>
                        {step.status === 'completed' ? (
                          <CheckCircle className="h-3 w-3 text-white" />
                        ) : step.status === 'processing' ? (
                          <Loader2 className="h-3 w-3 text-white animate-spin" />
                        ) : step.status === 'error' ? (
                          <AlertCircle className="h-3 w-3 text-white" />
                        ) : (
                          <div className="w-2 h-2 bg-white rounded-full" />
                        )}
                      </div>
                      <div className="flex-1">
                        <div className="text-sm font-medium">{step.name}</div>
                        {step.status === 'processing' && (
                          <Progress value={step.progress} className="mt-1" />
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {step === 'preview' && importedData.length > 0 && (
              <div className="mt-6">
                <Button
                  onClick={handleImportData}
                  disabled={isImporting}
                  className="w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white font-semibold shadow-lg hover:shadow-xl transition-all duration-300"
                >
                  {isImporting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Importing...
                    </>
                  ) : (
                    <>
                      <FileSpreadsheet className="mr-2 h-4 w-4" />
                      Import Data
                    </>
                  )}
                </Button>
              </div>
            )}
          </div>

          {/* Preview Section */}
          <div className="lg:col-span-2">
            {step === 'preview' && selectedSheet && (
              <div className="bg-white rounded-2xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300">
                <GoogleSheetsPreview
                  sheet={selectedSheet}
                  selectedSheetTab={selectedSheetTab}
                  onDataLoad={handleDataLoad}
                  onError={handleError}
                  onSheetTabChange={handleSheetTabChange}
                />
              </div>
            )}

            {step === 'import' && (
              <div className="bg-white rounded-2xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300">
                <div className="p-6">
                  <div className="flex items-center space-x-2 mb-6">
                    <Loader2 className="h-5 w-5 animate-spin text-purple-600" />
                    <h3 className="text-lg font-semibold text-gray-900">Importing Data</h3>
                  </div>
                  <div className="space-y-6">
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium text-gray-700">Overall Progress</span>
                        <span className="text-sm text-gray-600">{Math.round(getStepProgress())}%</span>
                      </div>
                      <Progress value={getStepProgress()} className="h-2" />
                    </div>
                  </div>
                </div>
              </div>
            )}

            {step === 'complete' && (
              <div className="bg-white rounded-2xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300">
                <div className="p-8 text-center">
                  <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <CheckCircle className="h-8 w-8 text-green-600" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">
                    Import Successful!
                  </h3>
                  <p className="text-gray-600 mb-6">
                    Your Google Sheet data has been successfully imported and is ready to use.
                  </p>
                  <div className="flex justify-center space-x-4">
                    <Button
                      onClick={() => router.push('/dashboard/data')}
                      className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700"
                    >
                      <FileSpreadsheet className="mr-2 h-4 w-4" />
                      View Data Sources
                    </Button>
                    <Button
                      variant="outline"
                      onClick={() => router.push('/dashboard/create-report')}
                    >
                      <Sparkles className="mr-2 h-4 w-4" />
                      Create Report
                    </Button>
                  </div>
                </div>
              </div>
            )}

            {step === 'connect' && (
              <div className="bg-white rounded-2xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300">
                <div className="flex items-center justify-center py-20">
                  <div className="text-center text-gray-500 max-w-md">
                    <div className="w-20 h-20 bg-gradient-to-br from-purple-100 to-pink-200 rounded-2xl mx-auto mb-6 flex items-center justify-center">
                      <FileSpreadsheet className="w-10 h-10 text-purple-600" />
                    </div>
                    <p className="text-xl font-medium text-gray-700 mb-2">Preview Your Data</p>
                    <p className="text-sm text-gray-500">Select a Google Sheet from the left to preview and import your data</p>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Error Alert */}
        {error && (
          <div className="mt-6">
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          </div>
        )}
      </div>
    </div>
  )
}
