'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  Download, 
  FileText, 
  Image, 
  FileSpreadsheet,
  Mail,
  Link as LinkIcon,
  Cloud,
  Share,
  Settings,
  Calendar,
  Clock,
  CheckCircle,
  AlertCircle,
  ArrowRight,
  Eye,
  Copy,
  Zap,
  Shield,
  Globe,
  Users
} from 'lucide-react'
import Link from 'next/link'
import { useAuth } from '@/components/auth/auth-provider'
import { getUserReports } from '@/lib/firestore'

interface ExportFormat {
  id: string
  name: string
  description: string
  icon: any
  fileExtension: string
  features: string[]
  category: 'document' | 'data' | 'image' | 'web'
  isPopular: boolean
}

interface ExportHistory {
  id: string
  reportName: string
  format: string
  exportedAt: string
  status: 'completed' | 'processing' | 'failed'
  fileSize: string
  downloadUrl?: string
}

export default function ExportPage() {
  const [selectedReport, setSelectedReport] = useState<string>('')
  const [selectedFormat, setSelectedFormat] = useState<string>('')
  const [exportSettings, setExportSettings] = useState({
    includeCharts: true,
    includeData: true,
    includeMetadata: false,
    quality: 'high'
  })

  const exportFormats: ExportFormat[] = [
    {
      id: 'pdf',
      name: 'PDF Document',
      description: 'Professional document format perfect for sharing and printing',
      icon: FileText,
      fileExtension: '.pdf',
      features: ['Print Ready', 'Universal Compatibility', 'Preserves Formatting', 'Secure'],
      category: 'document',
      isPopular: true
    },
    {
      id: 'excel',
      name: 'Excel Spreadsheet',
      description: 'Editable spreadsheet with data and charts for further analysis',
      icon: FileSpreadsheet,
      fileExtension: '.xlsx',
      features: ['Editable Data', 'Interactive Charts', 'Formulas Support', 'Multiple Sheets'],
      category: 'data',
      isPopular: true
    },
    {
      id: 'png',
      name: 'PNG Image',
      description: 'High-quality image format ideal for presentations and web',
      icon: Image,
      fileExtension: '.png',
      features: ['High Resolution', 'Transparent Background', 'Web Optimized', 'Lossless'],
      category: 'image',
      isPopular: false
    },
    {
      id: 'powerpoint',
      name: 'PowerPoint Presentation',
      description: 'Ready-to-present slides with your report content',
      icon: FileText,
      fileExtension: '.pptx',
      features: ['Slide Templates', 'Editable Content', 'Speaker Notes', 'Animation Ready'],
      category: 'document',
      isPopular: true
    },
    {
      id: 'csv',
      name: 'CSV Data',
      description: 'Raw data export for integration with other tools',
      icon: FileSpreadsheet,
      fileExtension: '.csv',
      features: ['Raw Data', 'Universal Format', 'Lightweight', 'Database Ready'],
      category: 'data',
      isPopular: false
    },
    {
      id: 'web',
      name: 'Web Dashboard',
      description: 'Interactive web version accessible via shareable link',
      icon: Globe,
      fileExtension: '.html',
      features: ['Interactive Charts', 'Real-time Updates', 'Mobile Responsive', 'Shareable Link'],
      category: 'web',
      isPopular: true
    }
  ]

  const [availableReports, setAvailableReports] = useState<any[]>([])
  const [exportHistory, setExportHistory] = useState<ExportHistory[]>([])
  const [isLoadingReports, setIsLoadingReports] = useState(false)
  const [isLoadingHistory, setIsLoadingHistory] = useState(false)
  const [reportsError, setReportsError] = useState<string>('')
  const [historyError, setHistoryError] = useState<string>('')

  const { user } = useAuth()

  // Load user's reports
  useEffect(() => {
    const loadReports = async () => {
      if (!user) return

      try {
        setIsLoadingReports(true)
        setReportsError('')

        const firebaseReports = await getUserReports(user.uid)

        const transformedReports = firebaseReports.map((report: any) => {
          const updatedAt = new Date(report.updated_at)
          const now = new Date()
          const diffInHours = Math.floor((now.getTime() - updatedAt.getTime()) / (1000 * 60 * 60))

          let lastUpdated: string
          if (diffInHours < 1) {
            lastUpdated = 'Just now'
          } else if (diffInHours < 24) {
            lastUpdated = `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`
          } else {
            const diffInDays = Math.floor(diffInHours / 24)
            lastUpdated = `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`
          }

          return {
            id: report.id,
            name: report.name,
            lastUpdated
          }
        })

        setAvailableReports(transformedReports)
      } catch (error) {
        console.error('Failed to load reports:', error)
        setReportsError('Failed to load your reports. Please try again.')
      } finally {
        setIsLoadingReports(false)
      }
    }

    loadReports()
  }, [user])

  // Load export history (placeholder - would need to implement export tracking)
  useEffect(() => {
    // For now, just set empty array since we don't have export history tracking yet
    setExportHistory([])
  }, [user])

  const getCategoryIcon = (category: string) => {
    const icons = {
      document: FileText,
      data: FileSpreadsheet,
      image: Image,
      web: Globe
    }
    return icons[category as keyof typeof icons] || FileText
  }

  const getCategoryColor = (category: string) => {
    const colors = {
      document: 'from-blue-500 to-cyan-600',
      data: 'from-emerald-500 to-teal-600',
      image: 'from-purple-500 to-pink-600',
      web: 'from-orange-500 to-red-600'
    }
    return colors[category as keyof typeof colors] || 'from-gray-500 to-slate-600'
  }

  const getStatusBadge = (status: string) => {
    const styles = {
      completed: 'bg-green-100 text-green-800',
      processing: 'bg-yellow-100 text-yellow-800',
      failed: 'bg-red-100 text-red-800'
    }
    const labels = {
      completed: 'Completed',
      processing: 'Processing',
      failed: 'Failed'
    }
    return (
      <Badge className={`${styles[status as keyof typeof styles]} hover:${styles[status as keyof typeof styles]}`}>
        {labels[status as keyof typeof labels]}
      </Badge>
    )
  }

  const handleExport = () => {
    if (!selectedReport || !selectedFormat) return
    // Export logic would go here
    console.log('Exporting:', { selectedReport, selectedFormat, exportSettings })
  }

  return (
    <div className="min-h-screen bg-slate-50">
      <div className="container mx-auto max-w-7xl px-6 py-8">
        {/* Hero Section */}
        <div className="relative overflow-hidden rounded-3xl bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900 p-8 mb-8">
          <div className="absolute inset-0 opacity-20">
            <div className="absolute inset-0" style={{
              backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%239C92AC' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='4'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
            }}></div>
          </div>
          <div className="relative z-10">
            <div className="max-w-2xl">
              <div className="flex items-center space-x-2 mb-4">
                <div className="flex items-center space-x-1">
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                  <span className="text-green-400 text-sm font-medium">Export Ready</span>
                </div>
              </div>
              <h1 className="text-4xl md:text-5xl font-bold text-white mb-4 leading-tight">
                Export & Share,
                <span className="block text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-purple-400">
                  Effortlessly
                </span>
              </h1>
              <p className="text-xl text-gray-300 mb-8 leading-relaxed">
                Export your reports in multiple formats and share them with your team or stakeholders
              </p>
              <div className="flex flex-wrap items-center gap-6 text-sm text-gray-400">
                <div className="flex items-center space-x-2">
                  <Shield className="h-4 w-4 text-green-400" />
                  <span>Secure Export</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Zap className="h-4 w-4 text-yellow-400" />
                  <span>Fast Processing</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Users className="h-4 w-4 text-blue-400" />
                  <span>Easy Sharing</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Export Configuration */}
          <div className="lg:col-span-2 space-y-6">
            {/* Report Selection */}
            <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300">
              <div className="mb-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-2">Select Report</h2>
                <p className="text-sm text-gray-600">Choose the report you want to export</p>
              </div>
              
              <div className="space-y-3">
                {availableReports.map((report) => (
                  <div
                    key={report.id}
                    className={`flex items-center justify-between p-4 rounded-xl cursor-pointer transition-all duration-300 ${
                      selectedReport === report.id 
                        ? 'bg-purple-50 border-2 border-purple-500' 
                        : 'bg-gray-50 hover:bg-gray-100 border-2 border-transparent'
                    }`}
                    onClick={() => setSelectedReport(report.id)}
                  >
                    <div className="flex items-center space-x-3">
                      <div className="p-2 bg-gradient-to-br from-blue-500 to-cyan-600 rounded-lg">
                        <FileText className="h-5 w-5 text-white" />
                      </div>
                      <div>
                        <p className="font-medium text-gray-900">{report.name}</p>
                        <p className="text-sm text-gray-500">Updated {report.lastUpdated}</p>
                      </div>
                    </div>
                    <Button size="sm" variant="ghost">
                      <Eye className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            </div>

            {/* Format Selection */}
            <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300">
              <div className="mb-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-2">Export Format</h2>
                <p className="text-sm text-gray-600">Choose how you want to export your report</p>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {exportFormats.map((format) => {
                  const Icon = format.icon
                  return (
                    <div
                      key={format.id}
                      className={`group relative p-4 rounded-xl cursor-pointer transition-all duration-300 ${
                        selectedFormat === format.id 
                          ? 'bg-purple-50 border-2 border-purple-500' 
                          : 'bg-gray-50 hover:bg-gray-100 border-2 border-transparent hover:-translate-y-1'
                      }`}
                      onClick={() => setSelectedFormat(format.id)}
                    >
                      {format.isPopular && (
                        <div className="absolute -top-2 -right-2">
                          <Badge className="bg-orange-100 text-orange-800 hover:bg-orange-100 text-xs">
                            Popular
                          </Badge>
                        </div>
                      )}
                      
                      <div className="flex items-start space-x-3">
                        <div className={`p-2 rounded-lg bg-gradient-to-br ${getCategoryColor(format.category)} group-hover:scale-110 transition-transform duration-300`}>
                          <Icon className="h-5 w-5 text-white" />
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-1">
                            <h3 className="font-semibold text-gray-900">{format.name}</h3>
                            <span className="text-xs text-gray-500 bg-gray-200 px-2 py-1 rounded">
                              {format.fileExtension}
                            </span>
                          </div>
                          <p className="text-sm text-gray-600 mb-3">{format.description}</p>
                          <div className="flex flex-wrap gap-1">
                            {format.features.slice(0, 2).map((feature, index) => (
                              <span key={index} className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                                {feature}
                              </span>
                            ))}
                          </div>
                        </div>
                      </div>
                    </div>
                  )
                })}
              </div>
            </div>

            {/* Export Settings */}
            {selectedFormat && (
              <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300">
                <div className="mb-6">
                  <h2 className="text-xl font-semibold text-gray-900 mb-2">Export Settings</h2>
                  <p className="text-sm text-gray-600">Customize your export preferences</p>
                </div>
                
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium text-gray-900">Include Charts</p>
                      <p className="text-sm text-gray-500">Export visual charts and graphs</p>
                    </div>
                    <input
                      type="checkbox"
                      checked={exportSettings.includeCharts}
                      onChange={(e) => setExportSettings({...exportSettings, includeCharts: e.target.checked})}
                      className="w-4 h-4 text-purple-600 rounded focus:ring-purple-500"
                    />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium text-gray-900">Include Raw Data</p>
                      <p className="text-sm text-gray-500">Export underlying data tables</p>
                    </div>
                    <input
                      type="checkbox"
                      checked={exportSettings.includeData}
                      onChange={(e) => setExportSettings({...exportSettings, includeData: e.target.checked})}
                      className="w-4 h-4 text-purple-600 rounded focus:ring-purple-500"
                    />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium text-gray-900">Include Metadata</p>
                      <p className="text-sm text-gray-500">Export creation date and settings</p>
                    </div>
                    <input
                      type="checkbox"
                      checked={exportSettings.includeMetadata}
                      onChange={(e) => setExportSettings({...exportSettings, includeMetadata: e.target.checked})}
                      className="w-4 h-4 text-purple-600 rounded focus:ring-purple-500"
                    />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium text-gray-900">Quality</p>
                      <p className="text-sm text-gray-500">Export quality and file size</p>
                    </div>
                    <select
                      value={exportSettings.quality}
                      onChange={(e) => setExportSettings({...exportSettings, quality: e.target.value})}
                      className="px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                    >
                      <option value="high">High Quality</option>
                      <option value="medium">Medium Quality</option>
                      <option value="low">Low Quality</option>
                    </select>
                  </div>
                </div>
                
                <div className="mt-8 flex justify-end">
                  <Button 
                    onClick={handleExport}
                    disabled={!selectedReport || !selectedFormat}
                    className="rounded-xl bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 disabled:opacity-50"
                  >
                    <Download className="mr-2 h-4 w-4" />
                    Export Report
                  </Button>
                </div>
              </div>
            )}
          </div>

          {/* Export History Sidebar */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300 sticky top-8">
              <div className="mb-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-2">Export History</h2>
                <p className="text-sm text-gray-600">Recent export activities</p>
              </div>
              
              <div className="space-y-4">
                {exportHistory.map((item) => (
                  <div key={item.id} className="p-4 bg-gray-50 rounded-xl">
                    <div className="flex items-start justify-between mb-2">
                      <div className="flex-1">
                        <p className="font-medium text-gray-900 text-sm truncate">{item.reportName}</p>
                        <p className="text-xs text-gray-500">{item.format} • {item.fileSize}</p>
                      </div>
                      {getStatusBadge(item.status)}
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <span className="text-xs text-gray-500">{item.exportedAt}</span>
                      {item.status === 'completed' && item.downloadUrl && (
                        <Button size="sm" variant="ghost" className="h-6 w-6 p-0">
                          <Download className="h-3 w-3" />
                        </Button>
                      )}
                    </div>
                  </div>
                ))}
              </div>
              
              <div className="mt-6 pt-6 border-t border-gray-100">
                <div className="text-center">
                  <p className="text-sm text-gray-500 mb-3">Need help with exports?</p>
                  <Button variant="outline" size="sm" className="rounded-xl">
                    View Documentation
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
