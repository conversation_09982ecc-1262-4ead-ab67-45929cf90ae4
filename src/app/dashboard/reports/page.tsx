'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  FileText,
  BarChart3,
  TrendingUp,
  Users,
  DollarSign,
  Target,
  Eye,
  Download,
  Share,
  MoreHorizontal,
  Search,
  Filter,
  Calendar,
  Star,
  Clock,
  ArrowRight,
  Plus,
  Grid3X3,
  List,
  Trash2,
  Edit,
  Copy,
  Loader2
} from 'lucide-react'
import Link from 'next/link'
import { useAuth } from '@/components/auth/auth-provider'
import { getUserReports } from '@/lib/firestore'
import { Report as FirebaseReport } from '@/lib/database.types'
import { ReportModal } from '@/components/reports/report-modal'

interface Report {
  id: string
  name: string
  description: string
  type: string
  status: 'completed' | 'generating' | 'failed'
  createdAt: string
  lastViewed: string
  views: number
  isStarred: boolean
  thumbnail: string
  dataSource: string
  size: string
}

export default function ReportsPage() {
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [searchQuery, setSearchQuery] = useState('')
  const [filterType, setFilterType] = useState<'all' | 'sales' | 'analytics' | 'financial' | 'operational'>('all')
  const [selectedReports, setSelectedReports] = useState<string[]>([])
  const [reports, setReports] = useState<Report[]>([])
  const [isLoadingReports, setIsLoadingReports] = useState(false)
  const [reportsError, setReportsError] = useState<string>('')
  const [selectedReportId, setSelectedReportId] = useState<string | null>(null)
  const [isModalOpen, setIsModalOpen] = useState(false)

  const { user } = useAuth()

  // Load user's reports
  useEffect(() => {
    const loadReports = async () => {
      if (!user) return

      try {
        setIsLoadingReports(true)
        setReportsError('')

        const firebaseReports = await getUserReports(user.uid)

        // Transform Firebase reports to match our interface
        const transformedReports: Report[] = firebaseReports.map((report: FirebaseReport) => {
          // Calculate time since creation
          const createdAt = new Date(report.created_at)
          const now = new Date()
          const diffInHours = Math.floor((now.getTime() - createdAt.getTime()) / (1000 * 60 * 60))

          let lastViewed: string
          if (diffInHours < 1) {
            lastViewed = 'Just now'
          } else if (diffInHours < 24) {
            lastViewed = `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`
          } else {
            const diffInDays = Math.floor(diffInHours / 24)
            lastViewed = `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`
          }

          // Get thumbnail based on report type
          const getThumbnail = (type: string) => {
            switch (type.toLowerCase()) {
              case 'sales': return '📊'
              case 'analytics': return '👥'
              case 'financial': return '💰'
              case 'operational': return '🎯'
              default: return '📈'
            }
          }

          return {
            id: report.id,
            name: report.name,
            description: report.description || 'No description available',
            type: report.type,
            status: report.status === 'published' ? 'completed' : report.status === 'draft' ? 'generating' : 'failed',
            createdAt: report.created_at,
            lastViewed,
            views: 0, // We don't track views yet
            isStarred: false, // We don't track starred status yet
            thumbnail: getThumbnail(report.type),
            dataSource: 'Unknown', // We need to get this from the dataset relationship
            size: 'Unknown' // We don't track size yet
          }
        })

        setReports(transformedReports)
      } catch (error) {
        console.error('Failed to load reports:', error)
        setReportsError('Failed to load your reports. Please try again.')
      } finally {
        setIsLoadingReports(false)
      }
    }

    loadReports()
  }, [user])

  const handleReportClick = (reportId: string) => {
    setSelectedReportId(reportId)
    setIsModalOpen(true)
  }

  const handleModalClose = () => {
    setIsModalOpen(false)
    setSelectedReportId(null)
  }



  const getTypeIcon = (type: string) => {
    const icons = {
      sales: TrendingUp,
      analytics: BarChart3,
      financial: DollarSign,
      operational: Target
    }
    const Icon = icons[type as keyof typeof icons] || FileText
    return <Icon className="h-5 w-5" />
  }

  const getTypeColor = (type: string) => {
    const colors = {
      sales: 'from-blue-500 to-cyan-600',
      analytics: 'from-emerald-500 to-teal-600',
      financial: 'from-purple-500 to-pink-600',
      operational: 'from-orange-500 to-red-600'
    }
    return colors[type as keyof typeof colors] || 'from-gray-500 to-slate-600'
  }

  const getStatusBadge = (status: string) => {
    const styles = {
      completed: 'bg-green-100 text-green-800',
      generating: 'bg-yellow-100 text-yellow-800',
      failed: 'bg-red-100 text-red-800'
    }
    const labels = {
      completed: 'Completed',
      generating: 'Generating',
      failed: 'Failed'
    }
    return (
      <Badge className={`${styles[status as keyof typeof styles]} hover:${styles[status as keyof typeof styles]}`}>
        {labels[status as keyof typeof labels]}
      </Badge>
    )
  }

  const filteredReports = reports.filter(report => {
    const matchesSearch = report.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         report.description.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesFilter = filterType === 'all' || report.type === filterType
    return matchesSearch && matchesFilter
  })

  const completedReports = reports.filter(r => r.status === 'completed').length
  const totalViews = reports.reduce((sum, r) => sum + r.views, 0)
  const starredReports = reports.filter(r => r.isStarred).length
  const completionPercentage = reports.length > 0 ? Math.round((completedReports / reports.length) * 100) : 0

  return (
    <div className="min-h-screen bg-slate-50">
      <div className="container mx-auto max-w-7xl px-6 py-8">
        {/* Hero Section */}
        <div className="relative overflow-hidden rounded-3xl bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900 p-8 mb-8">
          <div className="absolute inset-0 opacity-20">
            <div className="absolute inset-0" style={{
              backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%239C92AC' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='4'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
            }}></div>
          </div>
          <div className="relative z-10 flex items-center justify-between">
            <div className="max-w-2xl">
              <div className="flex items-center space-x-2 mb-4">
                <div className="flex items-center space-x-1">
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                  <span className="text-green-400 text-sm font-medium">Reports Ready</span>
                </div>
              </div>
              <h1 className="text-4xl md:text-5xl font-bold text-white mb-4 leading-tight">
                Your Reports,
                <span className="block text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-purple-400">
                  At a Glance
                </span>
              </h1>
              <p className="text-xl text-gray-300 mb-8 leading-relaxed">
                Access, manage, and share all your generated reports from one centralized dashboard
              </p>
              <Link href="/dashboard/create-report">
                <Button size="lg" className="bg-white text-gray-900 hover:bg-gray-100 font-semibold px-8 py-4 rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105">
                  <Plus className="mr-2 h-5 w-5" />
                  Create New Report
                </Button>
              </Link>
            </div>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300">
            <div className="flex items-center justify-between mb-3">
              <div className="p-3 bg-gradient-to-br from-blue-500 to-cyan-600 rounded-xl">
                <FileText className="h-6 w-6 text-white" />
              </div>
              <span className="text-xs font-medium text-blue-600 bg-blue-50 px-2 py-1 rounded-full">+2</span>
            </div>
            <div>
              <p className="text-2xl font-bold text-gray-900">{reports.length}</p>
              <p className="text-sm text-gray-500">Total Reports</p>
            </div>
          </div>

          <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300">
            <div className="flex items-center justify-between mb-3">
              <div className="p-3 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-xl">
                <BarChart3 className="h-6 w-6 text-white" />
              </div>
              <span className="text-xs font-medium text-emerald-600 bg-emerald-50 px-2 py-1 rounded-full">
                {completionPercentage}%
              </span>
            </div>
            <div>
              <p className="text-2xl font-bold text-gray-900">{completedReports}</p>
              <p className="text-sm text-gray-500">Completed</p>
            </div>
          </div>

          <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300">
            <div className="flex items-center justify-between mb-3">
              <div className="p-3 bg-gradient-to-br from-purple-500 to-pink-600 rounded-xl">
                <Eye className="h-6 w-6 text-white" />
              </div>
              <span className="text-xs font-medium text-purple-600 bg-purple-50 px-2 py-1 rounded-full">+15</span>
            </div>
            <div>
              <p className="text-2xl font-bold text-gray-900">{totalViews}</p>
              <p className="text-sm text-gray-500">Total Views</p>
            </div>
          </div>

          <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300">
            <div className="flex items-center justify-between mb-3">
              <div className="p-3 bg-gradient-to-br from-orange-500 to-red-600 rounded-xl">
                <Star className="h-6 w-6 text-white" />
              </div>
              <span className="text-xs font-medium text-orange-600 bg-orange-50 px-2 py-1 rounded-full">Starred</span>
            </div>
            <div>
              <p className="text-2xl font-bold text-gray-900">{starredReports}</p>
              <p className="text-sm text-gray-500">Favorites</p>
            </div>
          </div>
        </div>

        {/* Reports Management Section */}
        <div className="bg-white rounded-2xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300">
          {/* Header with Search and Controls */}
          <div className="p-6 border-b border-gray-100">
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
              <div>
                <h2 className="text-xl font-semibold text-gray-900">Your Reports</h2>
                <p className="text-sm text-gray-600 mt-1">Manage and organize your generated reports</p>
              </div>
              
              <div className="flex items-center space-x-3">
                {/* Search */}
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Search reports..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10 pr-4 py-2 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  />
                </div>
                
                {/* Filter */}
                <select
                  value={filterType}
                  onChange={(e) => setFilterType(e.target.value as any)}
                  className="px-4 py-2 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                >
                  <option value="all">All Types</option>
                  <option value="sales">Sales</option>
                  <option value="analytics">Analytics</option>
                  <option value="financial">Financial</option>
                  <option value="operational">Operational</option>
                </select>
                
                {/* View Mode Toggle */}
                <div className="flex items-center bg-gray-100 rounded-xl p-1">
                  <button
                    onClick={() => setViewMode('grid')}
                    className={`p-2 rounded-lg transition-colors ${
                      viewMode === 'grid' ? 'bg-white shadow-sm' : 'hover:bg-gray-200'
                    }`}
                  >
                    <Grid3X3 className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => setViewMode('list')}
                    className={`p-2 rounded-lg transition-colors ${
                      viewMode === 'list' ? 'bg-white shadow-sm' : 'hover:bg-gray-200'
                    }`}
                  >
                    <List className="h-4 w-4" />
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Reports Content */}
          <div className="p-6">
            {isLoadingReports ? (
              <div className="text-center py-12">
                <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-gray-400" />
                <p className="text-sm text-gray-600">Loading your reports...</p>
              </div>
            ) : reportsError ? (
              <div className="text-center py-12">
                <div className="text-red-600 mb-4">
                  <FileText className="h-8 w-8 mx-auto mb-2" />
                  <p className="text-sm">{reportsError}</p>
                </div>
                <Button
                  variant="outline"
                  onClick={() => window.location.reload()}
                  className="text-sm"
                >
                  Try Again
                </Button>
              </div>
            ) : filteredReports.length === 0 ? (
              <div className="text-center py-12">
                <div className="w-20 h-20 bg-gradient-to-br from-gray-100 to-gray-200 rounded-2xl mx-auto mb-6 flex items-center justify-center">
                  <FileText className="h-10 w-10 text-gray-400" />
                </div>
                <p className="text-xl font-medium text-gray-700 mb-2">
                  {searchQuery || filterType !== 'all' ? 'No reports found' : 'No reports created yet'}
                </p>
                <p className="text-sm text-gray-500 mb-6">
                  {searchQuery || filterType !== 'all' ? 'Try adjusting your search or filter' : 'Create your first report to get started'}
                </p>
                <Link href="/dashboard/create-report">
                  <Button className="rounded-xl bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700">
                    <Plus className="mr-2 h-4 w-4" />
                    Create Report
                  </Button>
                </Link>
              </div>
            ) : (
              <div className={viewMode === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6' : 'space-y-4'}>
                {filteredReports.map((report) => (
                  <div
                    key={report.id}
                    className={`group bg-gray-50 rounded-xl p-6 hover:bg-gray-100 transition-all duration-300 cursor-pointer ${
                      viewMode === 'list' ? 'flex items-center justify-between' : ''
                    }`}
                    onClick={() => handleReportClick(report.id)}
                  >
                    <div className={`${viewMode === 'list' ? 'flex items-center space-x-4 flex-1' : ''}`}>
                      <div className={`flex items-center justify-between ${viewMode === 'list' ? 'space-x-4' : 'mb-4'}`}>
                        <div className={`p-3 rounded-xl bg-gradient-to-br ${getTypeColor(report.type)} ${viewMode === 'list' ? '' : 'group-hover:scale-110 transition-transform duration-300'}`}>
                          <span className="text-2xl">{report.thumbnail}</span>
                        </div>
                        {viewMode === 'grid' && (
                          <div className="flex items-center space-x-2">
                            {report.isStarred && <Star className="h-4 w-4 text-yellow-500 fill-current" />}
                            {getStatusBadge(report.status)}
                          </div>
                        )}
                      </div>
                      
                      <div className={viewMode === 'list' ? 'flex-1' : ''}>
                        <h3 className="font-semibold text-gray-900 mb-1">{report.name}</h3>
                        <p className="text-sm text-gray-600 mb-3">{report.description}</p>
                        
                        {viewMode === 'grid' && (
                          <div className="space-y-2 text-xs text-gray-500">
                            <div className="flex items-center justify-between">
                              <span>Views: {report.views}</span>
                              <span>{report.lastViewed}</span>
                            </div>
                            <div className="flex items-center justify-between">
                              <span>{report.size}</span>
                              <span>{new Date(report.createdAt).toLocaleDateString()}</span>
                            </div>
                          </div>
                        )}
                      </div>
                      
                      {viewMode === 'list' && (
                        <div className="flex items-center space-x-6 text-sm text-gray-500">
                          <span>{report.views} views</span>
                          <span>{report.lastViewed}</span>
                          {getStatusBadge(report.status)}
                          {report.isStarred && <Star className="h-4 w-4 text-yellow-500 fill-current" />}
                        </div>
                      )}
                    </div>

                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Report Modal */}
      <ReportModal
        reportId={selectedReportId}
        isOpen={isModalOpen}
        onClose={handleModalClose}
      />
    </div>
  )
}
