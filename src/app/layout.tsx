import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import "../styles/animations.css";
import { AuthProvider } from "@/components/auth/auth-provider";
import { AnalyticsProvider } from "@/components/analytics/analytics-provider";
import { FontLoader } from "@/components/fonts/font-loader";
import { ToastProvider } from "@/components/ui/toast";
import Script from "next/script";

const inter = Inter({
  subsets: ["latin"],
  display: 'swap',
  variable: '--font-inter',
  preload: false, // Disable automatic preload to prevent warnings
  fallback: ['system-ui', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'sans-serif']
});

export const metadata: Metadata = {
  title: "Internal Analytics Platform - Company Reporting System",
  description: "Internal company analytics and reporting platform for authorized personnel only",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const measurementId = process.env.NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID || 'G-DGWZQP6CD4'
  const shouldLoadAnalytics = process.env.NODE_ENV === 'production' || process.env.NEXT_PUBLIC_ENABLE_ANALYTICS === 'true'

  return (
    <html lang="en" className={inter.variable}>
      <head>
        {shouldLoadAnalytics && (
          <>
            <Script
              src={`https://www.googletagmanager.com/gtag/js?id=${measurementId}`}
              strategy="afterInteractive"
            />
            <Script id="google-analytics" strategy="afterInteractive">
              {`
                window.dataLayer = window.dataLayer || [];
                function gtag(){dataLayer.push(arguments);}
                gtag('js', new Date());

                gtag('config', '${measurementId}', {
                  cookie_flags: '${process.env.NODE_ENV === 'development' ? 'SameSite=Lax' : 'SameSite=Lax;Secure'}',
                  cookie_domain: '${process.env.NODE_ENV === 'development' ? 'localhost' : 'auto'}',
                  cookie_expires: 63072000,
                  send_page_view: true,
                  anonymize_ip: true
                });
              `}
            </Script>
          </>
        )}
      </head>
      <body className={`${inter.variable} antialiased`}>
        <FontLoader />
        <AuthProvider>
          <AnalyticsProvider>
            <ToastProvider>
              {children}
            </ToastProvider>
          </AnalyticsProvider>
        </AuthProvider>
      </body>
    </html>
  );
}
