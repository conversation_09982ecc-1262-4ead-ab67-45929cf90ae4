'use client'

import { useEffect } from 'react'

declare global {
  interface Window {
    gtag: (...args: any[]) => void
    dataLayer: any[]
  }
}

export function AnalyticsProvider({ children }: { children: React.ReactNode }) {
  useEffect(() => {
    // Only log analytics status in development
    if (process.env.NODE_ENV === 'development') {
      const analyticsEnabled = process.env.NEXT_PUBLIC_ENABLE_ANALYTICS === 'true'
      console.log(`🔍 Analytics ${analyticsEnabled ? 'enabled' : 'disabled'} in development`)

      if (!analyticsEnabled) {
        console.log('💡 To enable analytics in development, set NEXT_PUBLIC_ENABLE_ANALYTICS=true in .env.local')
      }
    }
  }, [])

  return <>{children}</>
}
