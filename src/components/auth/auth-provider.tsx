'use client'

import { createContext, useContext, useEffect, useState } from 'react'
import { User } from 'firebase/auth'
import {
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  signOut as firebaseSignOut,
  sendPasswordResetEmail,
  onAuthStateChanged,
  updateProfile,
  signInWithPopup,
  GoogleAuthProvider
} from 'firebase/auth'
import { auth } from '@/lib/firebase'
import { usersService } from '@/lib/firestore'

interface AuthContextType {
  user: User | null
  loading: boolean
  signIn: (email: string, password: string) => Promise<{ error: any }>
  signInWithGoogle: () => Promise<{ error: any }>
  signUp: (email: string, password: string, fullName?: string) => Promise<{ error: any }>
  signOut: () => Promise<void>
  resetPassword: (email: string) => Promise<{ error: any }>
  refreshGoogleAccessToken: () => Promise<string | null>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Listen for auth state changes
    const unsubscribe = onAuthStateChanged(auth, async (firebaseUser) => {
      setUser(firebaseUser)

      // Create user document in Firestore if it doesn't exist
      if (firebaseUser) {
        try {
          const existingUser = await usersService.getById(firebaseUser.uid)
          if (!existingUser) {
            await usersService.create({
              id: firebaseUser.uid,
              email: firebaseUser.email || '',
              full_name: firebaseUser.displayName || null,
              avatar_url: firebaseUser.photoURL || null,
              role: 'user',
            })
          }
        } catch (error) {
          console.error('Error creating user document:', error)
        }
      }

      setLoading(false)
    })

    return () => unsubscribe()
  }, [])

  const signIn = async (email: string, password: string) => {
    try {
      await signInWithEmailAndPassword(auth, email, password)
      return { error: null }
    } catch (error: any) {
      return { error }
    }
  }

  const signInWithGoogle = async () => {
    try {
      const provider = new GoogleAuthProvider()
      provider.addScope('email')
      provider.addScope('profile')
      // Add Google Sheets API scopes for reading spreadsheet data
      provider.addScope('https://www.googleapis.com/auth/spreadsheets.readonly')
      provider.addScope('https://www.googleapis.com/auth/drive.readonly')

      const result = await signInWithPopup(auth, provider)
      const user = result.user

      // Get the Google OAuth access token
      const credential = GoogleAuthProvider.credentialFromResult(result)
      const accessToken = credential?.accessToken

      // Store the access token and timestamp in localStorage for Google API calls
      if (accessToken) {
        localStorage.setItem('google_access_token', accessToken)
        localStorage.setItem('google_access_token_timestamp', Date.now().toString())
        // Also store the refresh token if available
        if (credential?.idToken) {
          localStorage.setItem('google_id_token', credential.idToken)
        }
      }

      // Create or update user document in Firestore
      if (user) {
        try {
          const existingUser = await usersService.getById(user.uid)
          if (!existingUser) {
            await usersService.create({
              id: user.uid,
              email: user.email || '',
              full_name: user.displayName || null,
              avatar_url: user.photoURL || null,
              role: 'user',
            })
          }
        } catch (error) {
          console.error('Error creating user document:', error)
        }
      }

      return { error: null }
    } catch (error: any) {
      // Handle specific Google auth errors
      if (error.code === 'auth/popup-closed-by-user') {
        return { error: { message: 'Sign-in was cancelled' } }
      } else if (error.code === 'auth/popup-blocked') {
        return { error: { message: 'Pop-up was blocked by your browser. Please allow pop-ups and try again.' } }
      } else if (error.code === 'auth/cancelled-popup-request') {
        return { error: { message: 'Sign-in was cancelled' } }
      }
      return { error }
    }
  }

  const signUp = async (email: string, password: string, fullName?: string) => {
    try {
      const { user: firebaseUser } = await createUserWithEmailAndPassword(auth, email, password)

      // Update the user's display name if provided
      if (fullName && firebaseUser) {
        await updateProfile(firebaseUser, {
          displayName: fullName
        })
      }

      return { error: null }
    } catch (error: any) {
      return { error }
    }
  }

  const signOut = async () => {
    try {
      // Clear stored Google tokens
      localStorage.removeItem('google_access_token')
      localStorage.removeItem('google_access_token_timestamp')
      localStorage.removeItem('google_id_token')

      await firebaseSignOut(auth)
    } catch (error) {
      console.error('Error signing out:', error)
    }
  }

  const resetPassword = async (email: string) => {
    try {
      await sendPasswordResetEmail(auth, email)
      return { error: null }
    } catch (error: any) {
      return { error }
    }
  }

  const refreshGoogleAccessToken = async (): Promise<string | null> => {
    try {
      const user = auth.currentUser
      if (!user) {
        throw new Error('User not authenticated')
      }

      // Check if user has Google provider
      const providerData = user.providerData.find(
        provider => provider.providerId === 'google.com'
      )

      if (!providerData) {
        throw new Error('User not authenticated with Google')
      }

      // Check if the current token is still valid (less than 50 minutes old)
      const tokenTimestamp = localStorage.getItem('google_access_token_timestamp')
      const currentToken = localStorage.getItem('google_access_token')

      if (tokenTimestamp && currentToken) {
        const tokenAge = Date.now() - parseInt(tokenTimestamp)
        const fiftyMinutes = 50 * 60 * 1000 // 50 minutes in milliseconds

        if (tokenAge < fiftyMinutes) {
          // Token is still valid, return it
          return currentToken
        }
      }

      // Token is expired or missing, need to re-authenticate
      console.log('Google access token expired, user needs to re-authenticate')
      return null
    } catch (error) {
      console.error('Error refreshing Google access token:', error)
      return null
    }
  }

  const value = {
    user,
    loading,
    signIn,
    signInWithGoogle,
    signUp,
    signOut,
    resetPassword,
    refreshGoogleAccessToken,
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
