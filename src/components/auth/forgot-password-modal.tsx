'use client'

import { useState } from 'react'
import { useAuth } from './auth-provider'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Modal } from '@/components/ui/modal'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, Mail, CheckCircle, ArrowLeft } from 'lucide-react'

interface ForgotPasswordModalProps {
  isOpen: boolean
  onClose: () => void
}

export function ForgotPasswordModal({ isOpen, onClose }: ForgotPasswordModalProps) {
  const [email, setEmail] = useState('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState(false)
  const { resetPassword } = useAuth()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError('')

    if (!email) {
      setError('Please enter your email address')
      setLoading(false)
      return
    }

    const { error } = await resetPassword(email)
    
    if (error) {
      if (error.code === 'auth/user-not-found') {
        setError('No account found with this email address')
      } else if (error.code === 'auth/invalid-email') {
        setError('Please enter a valid email address')
      } else {
        setError(error.message || 'Failed to send reset email')
      }
    } else {
      setSuccess(true)
    }
    
    setLoading(false)
  }

  const resetForm = () => {
    setEmail('')
    setError('')
    setSuccess(false)
    setLoading(false)
  }

  const handleClose = () => {
    resetForm()
    onClose()
  }

  const handleBackToLogin = () => {
    resetForm()
    setSuccess(false)
  }

  if (success) {
    return (
      <Modal isOpen={isOpen} onClose={handleClose} title="Check Your Email" size="md">
        <div className="text-center py-8">
          <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-gray-900 mb-2">
            Reset Link Sent!
          </h3>
          <p className="text-gray-600 mb-6">
            We've sent a password reset link to <strong>{email}</strong>. 
            Please check your email and follow the instructions to reset your password.
          </p>
          
          <div className="space-y-3">
            <Button 
              onClick={handleBackToLogin}
              variant="outline"
              className="w-full"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Send Another Email
            </Button>
            
            <Button
              onClick={handleClose}
              className="w-full bg-slate-700 hover:bg-slate-800"
            >
              Close
            </Button>
          </div>
          
          <p className="text-sm text-gray-500 mt-4">
            Didn't receive the email? Check your spam folder or try again.
          </p>
        </div>
      </Modal>
    )
  }

  return (
    <Modal isOpen={isOpen} onClose={handleClose} title="Reset Password" size="md">
      <div className="space-y-6">
        <div className="text-center">
          <p className="text-gray-600">
            Enter your email address and we'll send you a link to reset your password.
          </p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {error && (
            <Alert variant="destructive" className="animate-in slide-in-from-top-2 duration-300">
              <AlertDescription className="text-sm">{error}</AlertDescription>
            </Alert>
          )}

          {/* Email Field */}
          <div className="space-y-2">
            <label htmlFor="reset-email" className="text-sm font-medium text-gray-700">
              Email Address
            </label>
            <div className="relative group">
              <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400 group-focus-within:text-slate-600 transition-colors" />
              <Input
                id="reset-email"
                type="email"
                placeholder="Enter your email address"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="w-full pl-11 pr-4 py-3 bg-gray-50 border border-gray-200 rounded-xl placeholder:text-gray-500 focus:bg-white focus:border-slate-400 focus:ring-2 focus:ring-slate-200 transition-all duration-200 text-gray-800"
                required
                autoFocus
              />
            </div>
          </div>

          {/* Send Reset Link Button */}
          <Button
            type="submit"
            className="w-full bg-gradient-to-r from-slate-700 to-slate-800 hover:from-slate-800 hover:to-gray-900 text-white font-semibold py-3 rounded-xl transition-all duration-200 transform hover:scale-[1.02] active:scale-[0.98] shadow-lg hover:shadow-xl"
            disabled={loading}
          >
            {loading ? (
              <>
                <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                Sending Reset Link...
              </>
            ) : (
              'Send Reset Link'
            )}
          </Button>
        </form>

        {/* Help Text */}
        <div className="text-center">
          <p className="text-sm text-gray-500">
            Remember your password?{' '}
            <button
              type="button"
              onClick={handleClose}
              className="text-slate-600 hover:text-slate-700 font-medium transition-colors"
            >
              Back to Sign In
            </button>
          </p>
        </div>
      </div>
    </Modal>
  )
}
