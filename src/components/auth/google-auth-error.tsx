'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Button } from '@/components/ui/button'
import { useAuth } from '@/components/auth/auth-provider'
import { RefreshCw, LogOut, AlertTriangle } from 'lucide-react'

interface GoogleAuthErrorProps {
  error: string
  onRetry?: () => void
  className?: string
}

export function GoogleAuthError({ error, onRetry, className }: GoogleAuthErrorProps) {
  const [isRefreshing, setIsRefreshing] = useState(false)
  const { signOut, signInWithGoogle } = useAuth()
  const router = useRouter()

  // Check if this is a token expiration error
  const isTokenExpired = error.toLowerCase().includes('expired') || 
                        error.toLowerCase().includes('invalid credentials') ||
                        error.toLowerCase().includes('unauthenticated')

  const handleReAuthenticate = async () => {
    setIsRefreshing(true)
    try {
      // Sign out first to clear any cached tokens
      await signOut()
      
      // Small delay to ensure cleanup is complete
      await new Promise(resolve => setTimeout(resolve, 500))
      
      // Attempt to sign in with Google again
      const { error } = await signInWithGoogle()
      
      if (error) {
        console.error('Re-authentication failed:', error)
      } else {
        // Success - retry the original operation if provided
        if (onRetry) {
          onRetry()
        }
      }
    } catch (error) {
      console.error('Error during re-authentication:', error)
    } finally {
      setIsRefreshing(false)
    }
  }

  const handleSignOut = async () => {
    await signOut()
    router.push('/')
  }

  if (isTokenExpired) {
    return (
      <Alert variant="destructive" className={className}>
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription className="space-y-4">
          <div>
            <p className="font-medium mb-2">Google Access Token Expired</p>
            <p className="text-sm">
              Your Google authentication has expired. This happens automatically after about 1 hour for security reasons.
              Please re-authenticate with Google to continue accessing your spreadsheets.
            </p>
          </div>
          
          <div className="flex flex-col sm:flex-row gap-2">
            <Button
              onClick={handleReAuthenticate}
              disabled={isRefreshing}
              className="bg-blue-600 hover:bg-blue-700 text-white"
              size="sm"
            >
              {isRefreshing ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Re-authenticating...
                </>
              ) : (
                <>
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Re-authenticate with Google
                </>
              )}
            </Button>
            
            <Button
              onClick={handleSignOut}
              variant="outline"
              size="sm"
              className="border-gray-300"
            >
              <LogOut className="h-4 w-4 mr-2" />
              Sign Out
            </Button>
          </div>
        </AlertDescription>
      </Alert>
    )
  }

  // For other Google-related errors
  return (
    <Alert variant="destructive" className={className}>
      <AlertTriangle className="h-4 w-4" />
      <AlertDescription className="space-y-4">
        <div>
          <p className="font-medium mb-2">Google Sheets Connection Error</p>
          <p className="text-sm">{error}</p>
        </div>
        
        {onRetry && (
          <div className="flex gap-2">
            <Button
              onClick={onRetry}
              variant="outline"
              size="sm"
              className="border-gray-300"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Try Again
            </Button>
          </div>
        )}
      </AlertDescription>
    </Alert>
  )
}

// Hook to detect and handle Google auth errors
export function useGoogleAuthError() {
  const [error, setError] = useState<string | null>(null)

  const handleError = (errorMessage: string) => {
    setError(errorMessage)
  }

  const clearError = () => {
    setError(null)
  }

  const isGoogleAuthError = (errorMessage: string): boolean => {
    const googleErrorKeywords = [
      'google access token',
      'expired',
      'invalid credentials',
      'unauthenticated',
      'google sheets',
      'sign in with google'
    ]
    
    return googleErrorKeywords.some(keyword => 
      errorMessage.toLowerCase().includes(keyword.toLowerCase())
    )
  }

  return {
    error,
    handleError,
    clearError,
    isGoogleAuthError
  }
}
