'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from './auth-provider'
import { Button } from '@/components/ui/button'
import { GoogleIcon } from '@/components/ui/google-icon'
import { Loader2 } from 'lucide-react'
import { cn } from '@/lib/utils'

interface GoogleLoginButtonProps {
  className?: string
  variant?: 'default' | 'outline' | 'ghost'
  size?: 'default' | 'sm' | 'lg'
  disabled?: boolean
  onSuccess?: () => void
  onError?: (error: string) => void
}

export function GoogleLoginButton({ 
  className,
  variant = 'outline',
  size = 'default',
  disabled = false,
  onSuccess,
  onError
}: GoogleLoginButtonProps) {
  const [loading, setLoading] = useState(false)
  const { signInWithGoogle } = useAuth()
  const router = useRouter()

  const handleGoogleSignIn = async () => {
    if (disabled || loading) return
    
    setLoading(true)
    
    try {
      const { error } = await signInWithGoogle()
      
      if (error) {
        onError?.(error.message || 'Failed to sign in with Google')
      } else {
        onSuccess?.()
        router.push('/dashboard')
      }
    } catch (error) {
      onError?.('An unexpected error occurred')
    } finally {
      setLoading(false)
    }
  }

  return (
    <Button
      type="button"
      variant={variant}
      size={size}
      onClick={handleGoogleSignIn}
      disabled={disabled || loading}
      className={cn(
        "relative overflow-hidden group",
        variant === 'outline' && "border-gray-300 hover:border-gray-400 bg-white hover:bg-gray-50",
        variant === 'default' && "bg-white hover:bg-gray-50 text-gray-700 border border-gray-300 hover:border-gray-400",
        "transition-all duration-200 hover:shadow-md",
        className
      )}
    >
      {/* Background gradient animation */}
      <div className="absolute inset-0 bg-gradient-to-r from-blue-500/5 via-green-500/5 via-yellow-500/5 to-red-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
      
      <div className="relative flex items-center justify-center space-x-3">
        {loading ? (
          <Loader2 className="h-5 w-5 animate-spin text-gray-600" />
        ) : (
          <GoogleIcon size={20} animated={!loading} />
        )}
        
        <span className={cn(
          "font-medium",
          loading ? "text-gray-400" : "text-gray-700 group-hover:text-gray-800"
        )}>
          {loading ? 'Signing in...' : 'Continue with Google'}
        </span>
      </div>
      
      {/* Subtle shine effect on hover */}
      <div className="absolute inset-0 -translate-x-full group-hover:translate-x-full transition-transform duration-1000 bg-gradient-to-r from-transparent via-white/20 to-transparent skew-x-12" />
    </Button>
  )
}

// Alternative compact version
export function GoogleLoginButtonCompact({ 
  className,
  disabled = false,
  onSuccess,
  onError
}: Omit<GoogleLoginButtonProps, 'variant' | 'size'>) {
  const [loading, setLoading] = useState(false)
  const { signInWithGoogle } = useAuth()
  const router = useRouter()

  const handleGoogleSignIn = async () => {
    if (disabled || loading) return
    
    setLoading(true)
    
    try {
      const { error } = await signInWithGoogle()
      
      if (error) {
        onError?.(error.message || 'Failed to sign in with Google')
      } else {
        onSuccess?.()
        router.push('/dashboard')
      }
    } catch (error) {
      onError?.('An unexpected error occurred')
    } finally {
      setLoading(false)
    }
  }

  return (
    <button
      type="button"
      onClick={handleGoogleSignIn}
      disabled={disabled || loading}
      className={cn(
        "relative flex items-center justify-center w-12 h-12 rounded-full bg-white border border-gray-300 hover:border-gray-400 hover:shadow-md transition-all duration-200 group",
        disabled && "opacity-50 cursor-not-allowed",
        className
      )}
    >
      {loading ? (
        <Loader2 className="h-5 w-5 animate-spin text-gray-600" />
      ) : (
        <GoogleIcon size={24} animated={!loading} />
      )}
      
      {/* Tooltip */}
      <div className="absolute -top-10 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap">
        Sign in with Google
      </div>
    </button>
  )
}
