'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from './auth-provider'
import { GoogleIcon } from '@/components/ui/google-icon'
import { Loader2 } from 'lucide-react'
import { cn } from '@/lib/utils'

interface SocialLoginButtonsProps {
  onError?: (error: string) => void
  disabled?: boolean
}

// Social media icons as SVG components
function FacebookIcon({ className }: { className?: string }) {
  return (
    <svg className={className} viewBox="0 0 24 24" fill="currentColor">
      <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
    </svg>
  )
}

function TwitterIcon({ className }: { className?: string }) {
  return (
    <svg className={className} viewBox="0 0 24 24" fill="currentColor">
      <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
    </svg>
  )
}

function GitHubIcon({ className }: { className?: string }) {
  return (
    <svg className={className} viewBox="0 0 24 24" fill="currentColor">
      <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
    </svg>
  )
}

function LinkedInIcon({ className }: { className?: string }) {
  return (
    <svg className={className} viewBox="0 0 24 24" fill="currentColor">
      <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
    </svg>
  )
}

function AppleIcon({ className }: { className?: string }) {
  return (
    <svg className={className} viewBox="0 0 24 24" fill="currentColor">
      <path d="M12.152 6.896c-.948 0-2.415-1.078-3.96-1.04-2.04.027-3.91 1.183-4.961 3.014-2.117 3.675-.546 9.103 1.519 12.09 1.013 1.454 2.208 3.09 3.792 3.039 1.52-.065 2.09-.987 3.935-.987 1.831 0 2.35.987 3.96.948 1.637-.026 2.676-1.48 3.676-2.948 1.156-1.688 1.636-3.325 1.662-3.415-.039-.013-3.182-1.221-3.22-4.857-.026-3.04 2.48-4.494 2.597-4.559-1.429-2.09-3.623-2.324-4.39-2.376-2-.156-3.675 1.09-4.61 1.09zM15.53 3.83c.843-1.012 1.4-2.427 1.245-3.83-1.207.052-2.662.805-3.532 1.818-.78.896-1.454 2.338-1.273 3.714 1.338.104 2.715-.688 3.559-1.701"/>
    </svg>
  )
}

export function SocialLoginButtons({ onError, disabled = false }: SocialLoginButtonsProps) {
  const [googleLoading, setGoogleLoading] = useState(false)
  const { signInWithGoogle } = useAuth()
  const router = useRouter()

  const handleGoogleSignIn = async () => {
    if (disabled || googleLoading) return
    
    setGoogleLoading(true)
    
    try {
      const { error } = await signInWithGoogle()
      
      if (error) {
        onError?.(error.message || 'Failed to sign in with Google')
      } else {
        router.push('/dashboard')
      }
    } catch (error) {
      onError?.('An unexpected error occurred')
    } finally {
      setGoogleLoading(false)
    }
  }

  const handleInactiveClick = (provider: string) => {
    onError?.(`${provider} login is coming soon!`)
  }

  return (
    <div className="flex justify-center space-x-4">
      {/* Google - Active */}
      <button
        type="button"
        onClick={handleGoogleSignIn}
        disabled={disabled || googleLoading}
        className={cn(
          "relative flex items-center justify-center w-12 h-12 rounded-full bg-white border border-gray-300 hover:border-gray-400 hover:shadow-md transition-all duration-200 group",
          disabled && "opacity-50 cursor-not-allowed"
        )}
        title="Sign in with Google"
      >
        {googleLoading ? (
          <Loader2 className="h-5 w-5 animate-spin text-gray-600" />
        ) : (
          <GoogleIcon size={20} animated={!googleLoading} />
        )}
      </button>

      {/* Facebook - Inactive */}
      <button
        type="button"
        onClick={() => handleInactiveClick('Facebook')}
        disabled={disabled}
        className={cn(
          "relative flex items-center justify-center w-12 h-12 rounded-full bg-gray-100 border border-gray-300 cursor-not-allowed transition-all duration-200",
          "hover:bg-gray-200"
        )}
        title="Facebook login coming soon"
      >
        <FacebookIcon className="h-5 w-5 text-gray-400" />
      </button>

      {/* Twitter/X - Inactive */}
      <button
        type="button"
        onClick={() => handleInactiveClick('Twitter')}
        disabled={disabled}
        className={cn(
          "relative flex items-center justify-center w-12 h-12 rounded-full bg-gray-100 border border-gray-300 cursor-not-allowed transition-all duration-200",
          "hover:bg-gray-200"
        )}
        title="Twitter login coming soon"
      >
        <TwitterIcon className="h-5 w-5 text-gray-400" />
      </button>

      {/* GitHub - Inactive */}
      <button
        type="button"
        onClick={() => handleInactiveClick('GitHub')}
        disabled={disabled}
        className={cn(
          "relative flex items-center justify-center w-12 h-12 rounded-full bg-gray-100 border border-gray-300 cursor-not-allowed transition-all duration-200",
          "hover:bg-gray-200"
        )}
        title="GitHub login coming soon"
      >
        <GitHubIcon className="h-5 w-5 text-gray-400" />
      </button>

      {/* LinkedIn - Inactive */}
      <button
        type="button"
        onClick={() => handleInactiveClick('LinkedIn')}
        disabled={disabled}
        className={cn(
          "relative flex items-center justify-center w-12 h-12 rounded-full bg-gray-100 border border-gray-300 cursor-not-allowed transition-all duration-200",
          "hover:bg-gray-200"
        )}
        title="LinkedIn login coming soon"
      >
        <LinkedInIcon className="h-5 w-5 text-gray-400" />
      </button>

      {/* Apple - Inactive */}
      <button
        type="button"
        onClick={() => handleInactiveClick('Apple')}
        disabled={disabled}
        className={cn(
          "relative flex items-center justify-center w-12 h-12 rounded-full bg-gray-100 border border-gray-300 cursor-not-allowed transition-all duration-200",
          "hover:bg-gray-200"
        )}
        title="Apple login coming soon"
      >
        <AppleIcon className="h-5 w-5 text-gray-400" />
      </button>
    </div>
  )
}
