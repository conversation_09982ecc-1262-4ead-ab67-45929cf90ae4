'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Eye, EyeOff, AlertTriangle, CheckCircle, Info } from 'lucide-react'
import { formatNumber } from '@/lib/utils'

interface DataPreviewProps {
  data: Record<string, any>[]
  schema: Record<string, string>
  validationResults?: {
    errors: string[]
    warnings: string[]
    isValid: boolean
    stats: {
      totalRows: number
      totalColumns: number
      nullPercentage: string
    }
  }
  showRawData?: boolean
  onToggleRawData?: () => void
}

export function DataPreview({
  data,
  schema,
  validationResults,
  showRawData = false,
  onToggleRawData
}: DataPreviewProps) {
  const [currentPage, setCurrentPage] = useState(0)
  const rowsPerPage = 10
  const totalPages = Math.ceil(data.length / rowsPerPage)
  
  const paginatedData = data.slice(
    currentPage * rowsPerPage,
    (currentPage + 1) * rowsPerPage
  )

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'number':
      case 'integer':
        return 'bg-blue-100 text-blue-800'
      case 'string':
        return 'bg-gray-100 text-gray-800'
      case 'date':
        return 'bg-green-100 text-green-800'
      case 'boolean':
        return 'bg-purple-100 text-purple-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const formatCellValue = (value: any, type: string) => {
    if (value === null || value === undefined || value === '') {
      return <span className="text-gray-400 italic">null</span>
    }
    
    if (type === 'number' || type === 'integer') {
      return formatNumber(Number(value))
    }
    
    return String(value)
  }

  return (
    <div className="space-y-6">
      {/* Validation Results */}
      {validationResults && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {validationResults.isValid ? (
                <CheckCircle className="h-5 w-5 text-green-600" />
              ) : (
                <AlertTriangle className="h-5 w-5 text-red-600" />
              )}
              Data Validation Results
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Stats */}
            <div className="grid grid-cols-3 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900">
                  {formatNumber(validationResults.stats.totalRows)}
                </div>
                <div className="text-sm text-gray-500">Rows</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900">
                  {validationResults.stats.totalColumns}
                </div>
                <div className="text-sm text-gray-500">Columns</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900">
                  {validationResults.stats.nullPercentage}%
                </div>
                <div className="text-sm text-gray-500">Missing Values</div>
              </div>
            </div>

            {/* Errors */}
            {validationResults.errors.length > 0 && (
              <div className="space-y-2">
                <h4 className="font-medium text-red-800 flex items-center gap-2">
                  <AlertTriangle className="h-4 w-4" />
                  Errors ({validationResults.errors.length})
                </h4>
                <div className="space-y-1">
                  {validationResults.errors.slice(0, 5).map((error, index) => (
                    <div key={index} className="text-sm text-red-700 bg-red-50 p-2 rounded">
                      {error}
                    </div>
                  ))}
                  {validationResults.errors.length > 5 && (
                    <div className="text-sm text-red-600">
                      ... and {validationResults.errors.length - 5} more errors
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Warnings */}
            {validationResults.warnings.length > 0 && (
              <div className="space-y-2">
                <h4 className="font-medium text-yellow-800 flex items-center gap-2">
                  <Info className="h-4 w-4" />
                  Warnings ({validationResults.warnings.length})
                </h4>
                <div className="space-y-1">
                  {validationResults.warnings.map((warning, index) => (
                    <div key={index} className="text-sm text-yellow-700 bg-yellow-50 p-2 rounded">
                      {warning}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Data Schema */}
      <Card>
        <CardHeader>
          <CardTitle>Data Schema</CardTitle>
          <CardDescription>
            Detected column types and structure
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            {Object.entries(schema).map(([column, type]) => (
              <Badge key={column} variant="outline" className={getTypeColor(type)}>
                {column}: {type}
              </Badge>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Data Preview */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Data Preview</CardTitle>
              <CardDescription>
                Showing {paginatedData.length} of {formatNumber(data.length)} rows
              </CardDescription>
            </div>
            {onToggleRawData && (
              <Button
                variant="outline"
                size="sm"
                onClick={onToggleRawData}
                className="flex items-center gap-2"
              >
                {showRawData ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                {showRawData ? 'Hide Raw Data' : 'Show Raw Data'}
              </Button>
            )}
          </div>
        </CardHeader>
        <CardContent>
          <ScrollArea className="w-full">
            <Table>
              <TableHeader>
                <TableRow>
                  {Object.keys(schema).map((column) => (
                    <TableHead key={column} className="whitespace-nowrap">
                      {column}
                    </TableHead>
                  ))}
                </TableRow>
              </TableHeader>
              <TableBody>
                {paginatedData.map((row, index) => (
                  <TableRow key={index}>
                    {Object.entries(schema).map(([column, type]) => (
                      <TableCell key={column} className="whitespace-nowrap">
                        {formatCellValue(row[column], type)}
                      </TableCell>
                    ))}
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </ScrollArea>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex items-center justify-between mt-4">
              <div className="text-sm text-gray-500">
                Page {currentPage + 1} of {totalPages}
              </div>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(Math.max(0, currentPage - 1))}
                  disabled={currentPage === 0}
                >
                  Previous
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(Math.min(totalPages - 1, currentPage + 1))}
                  disabled={currentPage === totalPages - 1}
                >
                  Next
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
