'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { GoogleIcon } from '@/components/ui/google-icon'
import { 
  FileSpreadsheet, 
  ExternalLink, 
  Loader2, 
  CheckCircle, 
  AlertCircle,
  RefreshCw,
  Link as LinkIcon
} from 'lucide-react'
import { useAuth } from '@/components/auth/auth-provider'
import { googleSheetsService } from '@/lib/google-sheets'
import { GoogleAuthError, useGoogleAuthError } from '@/components/auth/google-auth-error'

interface GoogleSheet {
  id: string
  name: string
  url: string
  lastModified: string
  sheets: Array<{
    id: number
    title: string
    rowCount: number
    columnCount: number
  }>
}

interface GoogleSheetsConnectProps {
  onSheetSelect: (sheet: GoogleSheet, selectedSheetTab: string) => void
  onError: (error: string) => void
}

export function GoogleSheetsConnect({ onSheetSelect, onError }: GoogleSheetsConnectProps) {
  const [isConnected, setIsConnected] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [sheets, setSheets] = useState<GoogleSheet[]>([])
  const [selectedSheet, setSelectedSheet] = useState<GoogleSheet | null>(null)
  const [selectedSheetTab, setSelectedSheetTab] = useState<string>('')
  const [sheetUrl, setSheetUrl] = useState('')
  const [connectionMethod, setConnectionMethod] = useState<'browse' | 'url'>('browse')
  const [displayedSheetsCount, setDisplayedSheetsCount] = useState(5) // Show 5 sheets initially
  const { user } = useAuth()
  const { error: authError, handleError: handleAuthError, clearError: clearAuthError, isGoogleAuthError } = useGoogleAuthError()

  // Check if user is already authenticated with Google
  useEffect(() => {
    if (user) {
      // Check if user has Google Sheets access
      checkGoogleSheetsAccess()
    }
  }, [user])

  const checkGoogleSheetsAccess = async () => {
    try {
      setIsLoading(true)

      // Use the new helper method to check access
      const hasAccess = await googleSheetsService.hasValidGoogleAccess()

      if (hasAccess) {
        setIsConnected(true)
        await loadUserSheets()
      } else {
        setIsConnected(false)
      }
    } catch (error) {
      console.error('Error checking Google Sheets access:', error)
      setIsConnected(false)
    } finally {
      setIsLoading(false)
    }
  }

  const connectToGoogle = async () => {
    try {
      setIsLoading(true)
      clearAuthError() // Clear any previous auth errors

      // Use the helper method to check if we have valid Google access
      const hasAccess = await googleSheetsService.hasValidGoogleAccess()

      if (!hasAccess) {
        // Check if user is signed in with Google at all
        const providerData = user?.providerData.find(
          provider => provider.providerId === 'google.com'
        )

        if (!providerData) {
          const errorMsg = 'Please sign in with Google first to access Google Sheets.'
          handleAuthError(errorMsg)
          onError(errorMsg)
        } else {
          const errorMsg = 'Google Sheets access requires re-authentication. Please sign out and sign in with Google again to grant access to your spreadsheets.'
          handleAuthError(errorMsg)
          onError(errorMsg)
        }
        return
      }

      setIsConnected(true)
      await loadUserSheets()
    } catch (error: any) {
      console.error('Error connecting to Google:', error)
      const errorMsg = error?.message || 'Failed to connect to Google Sheets. Please try again.'

      if (isGoogleAuthError(errorMsg)) {
        handleAuthError(errorMsg)
      }
      onError(errorMsg)
    } finally {
      setIsLoading(false)
    }
  }

  const loadUserSheets = async () => {
    try {
      setIsLoading(true)
      const userSheets = await googleSheetsService.getUserSheets()
      setSheets(userSheets)
    } catch (error: any) {
      console.error('Error loading sheets:', error)
      const errorMsg = error?.message || 'Failed to load your Google Sheets. Please try again.'

      if (isGoogleAuthError(errorMsg)) {
        handleAuthError(errorMsg)
      }
      onError(errorMsg)
    } finally {
      setIsLoading(false)
    }
  }

  const handleSheetUrlSubmit = async () => {
    if (!sheetUrl.trim()) {
      onError('Please enter a valid Google Sheets URL')
      return
    }

    try {
      setIsLoading(true)
      const sheet = await googleSheetsService.getSheetByUrl(sheetUrl)
      setSelectedSheet(sheet)
      if (sheet.sheets.length > 0) {
        const firstTabTitle = sheet.sheets[0].title
        setSelectedSheetTab(firstTabTitle)
        // Immediately trigger the preview with the first tab
        onSheetSelect(sheet, firstTabTitle)
      }
    } catch (error: any) {
      console.error('Error importing sheet:', error)
      const errorMsg = error?.message || 'Failed to import the Google Sheet. Please check the URL and try again.'

      if (isGoogleAuthError(errorMsg)) {
        handleAuthError(errorMsg)
      }
      onError(errorMsg)
    } finally {
      setIsLoading(false)
    }
  }

  const handleSheetSelect = async (sheet: GoogleSheet) => {
    try {
      setIsLoading(true)

      // If sheet doesn't have tabs loaded, load them now
      if (sheet.sheets.length === 0) {
        const sheetTabs = await googleSheetsService.getSheetTabs(sheet.id)
        sheet.sheets = sheetTabs
      }

      setSelectedSheet(sheet)
      if (sheet.sheets.length > 0) {
        const firstTabTitle = sheet.sheets[0].title
        setSelectedSheetTab(firstTabTitle)
        // Immediately trigger the preview with the first tab
        onSheetSelect(sheet, firstTabTitle)
      }
    } catch (error: any) {
      console.error('Error loading sheet tabs:', error)
      const errorMsg = error?.message || 'Failed to load sheet tabs'

      if (isGoogleAuthError(errorMsg)) {
        handleAuthError(errorMsg)
      }
      onError(errorMsg)
    } finally {
      setIsLoading(false)
    }
  }





  return (
    <div className="flex flex-col items-center justify-center min-h-[400px] p-8">
      {/* Icon */}
      <div className="w-16 h-16 bg-green-100 rounded-2xl flex items-center justify-center mb-6">
        <FileSpreadsheet className="h-8 w-8 text-green-600" />
      </div>

      {/* Title and Description */}
      <h3 className="text-xl font-semibold text-gray-900 mb-3 text-center">
        {isConnected ? 'Your Google Sheets' : 'Connect to Google Sheets'}
      </h3>
      <p className="text-gray-600 text-center mb-8 max-w-md">
        {isConnected
          ? 'Select a spreadsheet to import data from your Google Sheets'
          : 'Authorize access to your Google account to browse and import your spreadsheets'
        }
      </p>

      {/* Show Google Auth Error if present */}
      {authError && (
        <GoogleAuthError
          error={authError}
          onRetry={loadUserSheets}
          className="mb-6 w-full max-w-md"
        />
      )}

      {/* Content based on connection status */}
      {!isConnected ? (
        /* Connection Button */
        <Button
          onClick={connectToGoogle}
          disabled={isLoading}
          className="bg-green-600 hover:bg-green-700 text-white px-8 py-3 text-lg font-medium"
        >
          {isLoading ? (
            <>
              <Loader2 className="h-5 w-5 animate-spin mr-2" />
              Connecting...
            </>
          ) : (
            <>
              <GoogleIcon size={20} className="mr-2" />
              Connect to Google Sheets
            </>
          )}
        </Button>
      ) : (
        /* Connected State - Show tabs and content */
        <div className="w-full max-w-2xl">
          {/* Connection Status and Refresh */}
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-2">
              <CheckCircle className="h-5 w-5 text-green-600" />
              <span className="font-medium text-gray-900">Connected</span>
            </div>
            <Button variant="outline" size="sm" onClick={loadUserSheets} disabled={isLoading}>
              <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
          </div>

          {/* Connection Method Tabs */}
          <div className="flex space-x-4 mb-6">
            <Button
              variant={connectionMethod === 'browse' ? 'default' : 'outline'}
              onClick={() => setConnectionMethod('browse')}
              className="flex-1"
            >
              <FileSpreadsheet className="mr-2 h-4 w-4" />
              Browse My Sheets
            </Button>
            <Button
              variant={connectionMethod === 'url' ? 'default' : 'outline'}
              onClick={() => setConnectionMethod('url')}
              className="flex-1"
            >
              <LinkIcon className="mr-2 h-4 w-4" />
              Import by URL
            </Button>
          </div>

          {connectionMethod === 'browse' ? (
            <div className="space-y-4">
              {isLoading ? (
                <div className="text-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-gray-400" />
                  <p className="text-sm text-gray-600">Loading your Google Sheets...</p>
                </div>
              ) : sheets.length > 0 ? (
                <div className="space-y-4">
                  {/* Scrollable container with fixed height */}
                  <div className="max-h-96 overflow-y-auto space-y-4 pr-2">
                    {sheets.slice(0, displayedSheetsCount).map((sheet) => (
                      <div
                        key={sheet.id}
                        className={`p-4 border rounded-lg cursor-pointer transition-all duration-200 ${
                          selectedSheet?.id === sheet.id
                            ? 'border-green-500 bg-green-50'
                            : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                        }`}
                        onClick={() => handleSheetSelect(sheet)}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                              <FileSpreadsheet className="h-5 w-5 text-green-600" />
                            </div>
                            <div>
                              <h4 className="font-medium text-gray-900">{sheet.name}</h4>
                              <p className="text-sm text-gray-500">
                                Last modified {new Date(sheet.lastModified).toLocaleDateString()}
                              </p>
                            </div>
                          </div>
                          <div className="flex items-center space-x-2">
                            {selectedSheet?.id === sheet.id && (
                              <CheckCircle className="h-5 w-5 text-green-600" />
                            )}
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={(e) => {
                                e.stopPropagation()
                                window.open(sheet.url, '_blank')
                              }}
                            >
                              <ExternalLink className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* Show More Button */}
                  {displayedSheetsCount < sheets.length && (
                    <div className="text-center pt-4 border-t border-gray-100">
                      <Button
                        variant="outline"
                        onClick={() => setDisplayedSheetsCount(prev => Math.min(prev + 10, sheets.length))}
                        className="w-full"
                      >
                        Show More ({sheets.length - displayedSheetsCount} remaining)
                      </Button>
                    </div>
                  )}

                  {/* Show Less Button when more than initial count is displayed */}
                  {displayedSheetsCount > 5 && displayedSheetsCount >= sheets.length && (
                    <div className="text-center">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setDisplayedSheetsCount(5)}
                        className="text-gray-500 hover:text-gray-700"
                      >
                        Show Less
                      </Button>
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-center py-8">
                  <FileSpreadsheet className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-sm text-gray-600">No Google Sheets found in your account</p>
                </div>
              )}
            </div>
          ) : (
            <div className="space-y-4">
              <div>
                <Label htmlFor="sheet-url">Google Sheets URL</Label>
                <div className="flex space-x-2 mt-1">
                  <Input
                    id="sheet-url"
                    placeholder="https://docs.google.com/spreadsheets/d/..."
                    value={sheetUrl}
                    onChange={(e) => setSheetUrl(e.target.value)}
                    className="flex-1"
                  />
                  <Button onClick={handleSheetUrlSubmit} disabled={isLoading || !sheetUrl.trim()}>
                    {isLoading ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      'Import'
                    )}
                  </Button>
                </div>
              </div>
              
              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  Make sure the Google Sheet is shared with your account or set to "Anyone with the link can view"
                </AlertDescription>
              </Alert>
            </div>
          )}

          {/* Selected Sheet Info */}
          {selectedSheet && (
            <div className="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                  <FileSpreadsheet className="h-5 w-5 text-green-600" />
                </div>
                <div>
                  <h4 className="font-medium text-green-900">{selectedSheet.name}</h4>
                  <p className="text-sm text-green-700">
                    {selectedSheet.sheets.length > 0
                      ? `${selectedSheet.sheets.length} sheet${selectedSheet.sheets.length !== 1 ? 's' : ''} available`
                      : 'Loading sheet information...'
                    }
                  </p>
                </div>
              </div>
            </div>
          )}


        </div>
      )}
    </div>
  )
}
