'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import {
  Activity,
  Database,
  Clock,
  CheckCircle,
  AlertCircle,
  XCircle,
  Play,
  Pause,
  RefreshCw,
  TrendingUp,
  TrendingDown,
  Minus
} from 'lucide-react'
import { SyncLog, ExternalFirebaseConnection } from '@/lib/database.types'
import { getUserSyncLogs, getConnectionSyncLogs } from '@/lib/firestore'
import { firebaseSyncEngine } from '@/lib/firebase-sync'
import { multiFirebaseManager } from '@/lib/multi-firebase'

interface SyncDashboardProps {
  connections: ExternalFirebaseConnection[]
  userId: string
}

interface SyncStats {
  totalConnections: number
  activeConnections: number
  totalSyncs: number
  successfulSyncs: number
  failedSyncs: number
  documentsProcessed: number
  lastSyncTime?: Date
}

export function SyncDashboard({ connections, userId }: SyncDashboardProps) {
  const [syncLogs, setSyncLogs] = useState<SyncLog[]>([])
  const [stats, setStats] = useState<SyncStats>({
    totalConnections: 0,
    activeConnections: 0,
    totalSyncs: 0,
    successfulSyncs: 0,
    failedSyncs: 0,
    documentsProcessed: 0
  })
  const [loading, setLoading] = useState(true)
  const [refreshing, setRefreshing] = useState(false)

  useEffect(() => {
    loadDashboardData()
  }, [connections, userId])

  const loadDashboardData = async () => {
    try {
      setLoading(true)

      // Load sync logs
      console.log(`📊 Loading sync logs for user: ${userId}`)
      const logs = await getUserSyncLogs(userId)
      console.log(`📋 Found ${logs.length} sync logs:`, logs)
      setSyncLogs(logs)

      // Calculate stats
      const activeSessions = firebaseSyncEngine.getActiveSessions()
      const activeConnectionCount = Array.from(activeSessions.keys()).length

      const completedSyncs = logs.filter(log => log.status === 'completed')
      const failedSyncs = logs.filter(log => log.status === 'failed')
      const totalDocuments = logs.reduce((sum, log) => sum + (log.records_processed || 0), 0)
      const lastSync = logs.length > 0 ? new Date(logs[0].started_at) : undefined

      setStats({
        totalConnections: connections.length,
        activeConnections: activeConnectionCount,
        totalSyncs: logs.length,
        successfulSyncs: completedSyncs.length,
        failedSyncs: failedSyncs.length,
        documentsProcessed: totalDocuments,
        lastSyncTime: lastSync
      })

    } catch (error) {
      console.error('Failed to load dashboard data:', error)
    } finally {
      setLoading(false)
    }
  }

  const refreshDashboard = async () => {
    setRefreshing(true)
    await loadDashboardData()
    setRefreshing(false)
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'failed':
        return <XCircle className="h-4 w-4 text-red-500" />
      case 'started':
        return <Play className="h-4 w-4 text-blue-500" />
      case 'partial':
        return <AlertCircle className="h-4 w-4 text-yellow-500" />
      default:
        return <Clock className="h-4 w-4 text-gray-500" />
    }
  }

  const getStatusBadge = (status: string) => {
    const styles = {
      completed: 'bg-green-100 text-green-800',
      failed: 'bg-red-100 text-red-800',
      started: 'bg-blue-100 text-blue-800',
      partial: 'bg-yellow-100 text-yellow-800'
    }
    return (
      <Badge className={`${styles[status as keyof typeof styles]} hover:${styles[status as keyof typeof styles]}`}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    )
  }

  const formatDuration = (ms: number) => {
    if (ms < 1000) return `${ms}ms`
    if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`
    return `${(ms / 60000).toFixed(1)}m`
  }

  const formatRelativeTime = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffMs = now.getTime() - date.getTime()
    const diffMins = Math.floor(diffMs / 60000)
    const diffHours = Math.floor(diffMins / 60)
    const diffDays = Math.floor(diffHours / 24)

    if (diffMins < 1) return 'Just now'
    if (diffMins < 60) return `${diffMins}m ago`
    if (diffHours < 24) return `${diffHours}h ago`
    return `${diffDays}d ago`
  }

  const getSuccessRate = () => {
    if (stats.totalSyncs === 0) return 0
    return Math.round((stats.successfulSyncs / stats.totalSyncs) * 100)
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
              <div className="animate-pulse">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-8 bg-gray-200 rounded w-1/2"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Sync Dashboard</h2>
          <p className="text-gray-600">Monitor your Firebase synchronization activity</p>
        </div>
        <Button
          onClick={refreshDashboard}
          disabled={refreshing}
          variant="outline"
          size="sm"
          className="border-gray-200 text-gray-700 hover:bg-gray-50"
        >
          {refreshing ? (
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-700 mr-2" />
          ) : (
            <RefreshCw className="h-4 w-4 mr-2" />
          )}
          Refresh
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Connections</p>
              <p className="text-2xl font-bold text-gray-900">{stats.totalConnections}</p>
            </div>
            <Database className="h-8 w-8 text-purple-500" />
          </div>
          <div className="mt-2 flex items-center text-sm">
            <span className="text-green-600 mr-1">{stats.activeConnections}</span>
            <span className="text-gray-500">active</span>
          </div>
        </div>

        <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Success Rate</p>
              <p className="text-2xl font-bold text-gray-900">{getSuccessRate()}%</p>
            </div>
            <TrendingUp className="h-8 w-8 text-green-500" />
          </div>
          <div className="mt-2">
            <Progress value={getSuccessRate()} className="h-2" />
          </div>
        </div>

        <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Syncs</p>
              <p className="text-2xl font-bold text-gray-900">{stats.totalSyncs}</p>
            </div>
            <Activity className="h-8 w-8 text-blue-500" />
          </div>
          <div className="mt-2 flex items-center text-sm">
            <span className="text-red-600 mr-1">{stats.failedSyncs}</span>
            <span className="text-gray-500">failed</span>
          </div>
        </div>

        <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Documents Synced</p>
              <p className="text-2xl font-bold text-gray-900">{stats.documentsProcessed.toLocaleString()}</p>
            </div>
            <CheckCircle className="h-8 w-8 text-cyan-500" />
          </div>
          <div className="mt-2 text-sm text-gray-500">
            {stats.lastSyncTime ? `Last sync ${formatRelativeTime(stats.lastSyncTime.toISOString())}` : 'No syncs yet'}
          </div>
        </div>
      </div>

      {/* Recent Sync Activity */}
      <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300 mb-8">
        <div className="mb-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Recent Sync Activity</h3>
          <p className="text-sm text-gray-600">
            Latest synchronization operations across all connections
          </p>
        </div>
        {syncLogs.length === 0 ? (
          <div className="text-center py-8">
            <Activity className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600">No sync activity yet</p>
            <p className="text-sm text-gray-500 mt-1">
              Sync logs will appear here once you start synchronizing data
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {syncLogs.slice(0, 10).map((log) => {
              const connection = connections.find(c => c.id === log.connection_id)
              return (
                <div key={log.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-200">
                  <div className="flex items-center space-x-4">
                    {getStatusIcon(log.status)}
                    <div>
                      <div className="flex items-center space-x-2">
                        <span className="font-medium text-gray-900">
                          {connection?.name || 'Unknown Connection'}
                        </span>
                        {getStatusBadge(log.status)}
                      </div>
                      <div className="text-sm text-gray-600 mt-1">
                        {log.collections_synced.join(', ')} • {log.sync_type} sync
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-sm text-gray-900">
                      {log.records_processed} records
                    </div>
                    <div className="text-xs text-gray-500">
                      {formatRelativeTime(log.started_at)}
                      {log.sync_duration_ms && ` • ${formatDuration(log.sync_duration_ms)}`}
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        )}
      </div>

      {/* Active Sync Sessions */}
      <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300">
        <div className="mb-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Active Sync Sessions</h3>
          <p className="text-sm text-gray-600">
            Currently running synchronization sessions
          </p>
        </div>
        {stats.activeConnections === 0 ? (
          <div className="text-center py-8">
            <Pause className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600">No active sync sessions</p>
            <p className="text-sm text-gray-500 mt-1">
              Start a sync session from your connections to see real-time activity
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {Array.from(firebaseSyncEngine.getActiveSessions().entries()).map(([connectionId, session]) => {
              const connection = connections.find(c => c.id === connectionId)
              return (
                <div key={connectionId} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-200">
                  <div className="flex items-center space-x-4">
                    <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                    <div>
                      <div className="font-medium text-gray-900">
                        {connection?.name || 'Unknown Connection'}
                      </div>
                      <div className="text-sm text-gray-600">
                        {session.syncRules.length} collections • Last sync {formatRelativeTime(session.lastSync.toISOString())}
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-sm text-gray-900">
                      {session.stats.documentsSuccess}/{session.stats.documentsProcessed} processed
                    </div>
                    <div className="text-xs text-gray-500">
                      {session.stats.documentsFailed} failed
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        )}
      </div>
    </div>
  )
}
