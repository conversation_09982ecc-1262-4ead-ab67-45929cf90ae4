'use client'

import { useEffect } from 'react'

export function FontLoader() {
  useEffect(() => {
    // Optimize font loading to prevent preload warnings
    if (typeof window !== 'undefined') {
      // Immediately apply font to ensure it's used
      const applyFont = () => {
        document.documentElement.style.setProperty('--font-family', 'var(--font-inter), system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif')
      }

      // Apply font immediately
      applyFont()

      // Also apply when fonts are ready
      if ('fonts' in document) {
        document.fonts.ready.then(applyFont)
      }
    }
  }, [])

  return null
}
