'use client'

import { useState, useEffect, useRef } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { useAuth } from '@/components/auth/auth-provider'
import { Button } from '@/components/ui/button'
import { UserAvatar } from '@/components/ui/user-avatar'
import {
  BarChart3,
  Database,
  FileText,
  Upload,
  Menu,
  X,
  LogOut,

  ChevronDown,
  FolderOpen,
  Settings,
  Download
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface DashboardLayoutProps {
  children: React.ReactNode
}

const navigation = [
  { name: 'Dashboard', href: '/dashboard', icon: BarChart3 },
  { name: 'Import Data', href: '/dashboard/import', icon: Upload },
  { name: 'My Files', href: '/dashboard/files', icon: FolderOpen },
  { name: 'Create Report', href: '/dashboard/create-report', icon: FileText },
  { name: 'My Reports', href: '/dashboard/reports', icon: Database },
  { name: 'Export', href: '/dashboard/export', icon: Download },
]

export function DashboardLayout({ children }: DashboardLayoutProps) {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)
  const [userMenuOpen, setUserMenuOpen] = useState(false)
  const pathname = usePathname()
  const { user, signOut } = useAuth()
  const userMenuRef = useRef<HTMLDivElement>(null)

  const handleSignOut = async () => {
    await signOut()
  }



  // Close user menu when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (userMenuRef.current && !userMenuRef.current.contains(event.target as Node)) {
        setUserMenuOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-purple-50">
      {/* Background blur overlay when user menu is open */}
      <div className={`fixed inset-0 z-40 transition-all duration-300 backdrop-blur-animate ${
        userMenuOpen
          ? 'bg-black/10 backdrop-blur-sm opacity-100'
          : 'bg-transparent backdrop-blur-none opacity-0 pointer-events-none'
      }`} />

      {/* Header */}
      <header className="fixed top-0 left-1/2 transform -translate-x-1/2 z-50 w-[85%] max-w-7xl mt-6 rounded-3xl bg-white/80 backdrop-blur-xl border border-gray-100 shadow-2xl">
        <div className="px-8 lg:px-12">
          <div className="flex justify-center items-center h-20">
            {/* Desktop Navigation - Centered with flexible spacing */}
            <nav className="hidden md:flex items-center justify-center flex-1 max-w-4xl mx-auto">
              <div className="flex items-center justify-between w-full px-4">
                {navigation.map((item) => {
                  const isActive = pathname === item.href
                  return (
                    <Link
                      key={item.name}
                      href={item.href}
                      className={cn(
                        "group flex items-center px-4 py-3 text-sm font-medium rounded-2xl transition-all duration-300",
                        "hover:scale-105 hover:-translate-y-0.5 whitespace-nowrap",
                        isActive
                          ? "text-gray-900 shadow-xl bg-white border border-gray-200/50 shadow-gray-200/50"
                          : "text-gray-600 hover:bg-gray-50/80 hover:text-gray-900 hover:shadow-lg hover:shadow-gray-200/30"
                      )}
                    >
                      <item.icon
                        className={cn(
                          "mr-2 h-4 w-4 transition-all duration-300",
                          isActive ? "text-gray-700" : "text-gray-500 group-hover:text-gray-700"
                        )}
                      />
                      <span className="font-medium">{item.name}</span>
                    </Link>
                  )
                })}
              </div>
            </nav>

            {/* User Menu - Positioned absolutely */}
            <div
              className="hidden md:block absolute right-8 top-1/2 transform -translate-y-1/2"
              ref={userMenuRef}
              onMouseEnter={() => setUserMenuOpen(true)}
              onMouseLeave={() => setUserMenuOpen(false)}
            >
              <div className="p-3 rounded-2xl hover:bg-gray-50/80 transition-all duration-300 cursor-pointer hover:scale-110 hover:-translate-y-0.5 hover:shadow-lg hover-lift">
                <UserAvatar
                  user={user}
                  className={`ring-2 transition-all duration-300 shadow-lg ${
                    userMenuOpen
                      ? 'ring-purple-300/70 shadow-purple-200/50'
                      : 'ring-gray-200/50 hover:ring-gray-300/70'
                  }`}
                />
              </div>

              {/* User Dropdown with enhanced animations */}
              <div className={`absolute right-0 mt-3 w-64 transition-all duration-300 ease-out transform z-50 ${
                userMenuOpen
                  ? 'opacity-100 scale-100 translate-y-0'
                  : 'opacity-0 scale-95 -translate-y-2 pointer-events-none'
              }`}>
                <div className={`bg-white/95 backdrop-blur-xl rounded-3xl shadow-2xl py-3 border border-gray-100/50 overflow-hidden ${
                  userMenuOpen ? 'animate-scale-in' : ''
                }`}>
                  <div className="px-6 py-4 border-b border-gray-100/50">
                    <p className={`text-sm font-semibold text-gray-900 transition-all duration-300 ${
                      userMenuOpen ? 'animate-slide-in-left animate-delay-100' : 'opacity-0'
                    }`}>
                      {user?.displayName || user?.email}
                    </p>
                    <p className={`text-xs text-gray-500 mt-1 transition-all duration-300 ${
                      userMenuOpen ? 'animate-slide-in-left animate-delay-200' : 'opacity-0'
                    }`}>
                      {user?.email}
                    </p>
                  </div>
                  <div className={`p-2 transition-all duration-300 ${
                    userMenuOpen ? 'animate-slide-in-bottom animate-delay-300' : 'opacity-0'
                  }`}>
                    <Button
                      variant="ghost"
                      className="w-full justify-start px-4 py-3 text-sm text-gray-700 hover:bg-gray-50/80 rounded-2xl transition-all duration-200 hover:scale-105 group hover-lift"
                      onClick={handleSignOut}
                    >
                      <LogOut className="mr-3 h-4 w-4 group-hover:rotate-12 transition-transform duration-200" />
                      Sign Out
                    </Button>
                  </div>
                </div>
              </div>
            </div>

            {/* Mobile menu button - Positioned absolutely */}
            <Button
              variant="ghost"
              size="sm"
              className="md:hidden absolute right-6 top-1/2 transform -translate-y-1/2 p-3 rounded-2xl bg-gray-50/80 hover:bg-gray-100/80 transition-all duration-300 hover:scale-110 hover:shadow-lg"
              onClick={() => setMobileMenuOpen(true)}
            >
              <Menu className="h-5 w-5 text-gray-600" />
            </Button>
          </div>
        </div>
      </header>

      {/* Mobile Menu */}
      <div className={cn(
        "fixed inset-0 z-50 md:hidden",
        mobileMenuOpen ? "block" : "hidden"
      )}>
        <div className="fixed inset-0 bg-black/30 backdrop-blur-md" onClick={() => setMobileMenuOpen(false)} />
        <div className="fixed inset-y-0 right-0 flex w-80 flex-col bg-white/95 backdrop-blur-xl shadow-2xl border-l border-gray-100">
          <div className="flex h-20 items-center justify-end px-6 border-b border-gray-100/50">
            <Button
              variant="ghost"
              size="sm"
              className="p-3 rounded-2xl bg-gray-50/80 hover:bg-gray-100/80 transition-all duration-300 hover:scale-110"
              onClick={() => setMobileMenuOpen(false)}
            >
              <X className="h-5 w-5 text-gray-600" />
            </Button>
          </div>

          <nav className="flex-1 space-y-3 px-6 py-8">
            {navigation.map((item) => {
              const isActive = pathname === item.href
              return (
                <Link
                  key={item.name}
                  href={item.href}
                  className={cn(
                    "group flex items-center px-6 py-4 text-sm font-medium rounded-2xl transition-all duration-300",
                    isActive
                      ? "bg-gray-100/80 text-gray-900 shadow-lg border border-gray-200/50"
                      : "text-gray-600 hover:bg-gray-50/80 hover:text-gray-900 hover:scale-105 hover:shadow-md"
                  )}
                  onClick={() => setMobileMenuOpen(false)}
                >
                  <item.icon
                    className={cn(
                      "mr-4 h-5 w-5 transition-all duration-300",
                      isActive ? "text-gray-700" : "text-gray-500 group-hover:text-gray-700"
                    )}
                  />
                  <span className="font-medium">{item.name}</span>
                </Link>
              )
            })}
          </nav>

          {/* Mobile User Menu */}
          <div className="flex-shrink-0 border-t border-gray-100/50 p-6">
            <div className="flex items-center bg-gray-50/80 rounded-2xl p-4">
              <UserAvatar
                user={user}
                size="lg"
                className="ring-2 ring-gray-200/50 shadow-lg"
              />
              <div className="ml-4 flex-1">
                <p className="text-sm font-semibold text-gray-900">
                  {user?.displayName || user?.email}
                </p>
                <p className="text-xs text-gray-500 mt-1">{user?.email}</p>
              </div>
              <Button
                variant="ghost"
                size="sm"
                className="p-3 rounded-2xl bg-gray-100/80 hover:bg-gray-200/80 transition-all duration-300 hover:scale-110"
                onClick={handleSignOut}
              >
                <LogOut className="h-4 w-4 text-gray-600" />
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Main content with top padding for fixed header */}
      <main className="flex-1 pt-24">
        {children}
      </main>
    </div>
  )
}
