'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Modal } from '@/components/ui/modal'
import { 
  Download,
  Share,
  Edit,
  MoreHorizontal,
  Database,
  BarChart3,
  TrendingUp,
  Users,
  DollarSign,
  Target,
  Loader2,
  AlertCircle,
  X
} from 'lucide-react'
import { useAuth } from '@/components/auth/auth-provider'
import { reportsService, getUserDataSources } from '@/lib/firestore'
import { Report as FirebaseReport, DataSource as FirebaseDataSource } from '@/lib/database.types'
import { ReportVisualization } from './report-visualization'

interface ReportModalProps {
  reportId: string | null
  isOpen: boolean
  onClose: () => void
}

export function ReportModal({ reportId, isOpen, onClose }: ReportModalProps) {
  const { user } = useAuth()
  const [report, setReport] = useState<FirebaseReport | null>(null)
  const [dataSource, setDataSource] = useState<FirebaseDataSource | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string>('')

  useEffect(() => {
    const loadReport = async () => {
      if (!user || !reportId || !isOpen) return

      try {
        setIsLoading(true)
        setError('')

        // Load the report
        const reportData = await reportsService.getById(reportId)
        if (!reportData) {
          setError('Report not found')
          return
        }

        // Check if user owns this report
        if (reportData.user_id !== user.uid) {
          setError('You do not have permission to view this report')
          return
        }

        setReport(reportData)

        // Load the associated data source
        try {
          const userDataSources = await getUserDataSources(user.uid)
          const associatedDataSource = userDataSources.find(ds => ds.id === reportData.dataset_id)
          if (associatedDataSource) {
            setDataSource(associatedDataSource)
          }
        } catch (dsError) {
          console.warn('Could not load data source:', dsError)
        }

      } catch (error) {
        console.error('Error loading report:', error)
        setError('Failed to load report. Please try again.')
      } finally {
        setIsLoading(false)
      }
    }

    if (isOpen && reportId) {
      loadReport()
    } else {
      // Reset state when modal closes
      setReport(null)
      setDataSource(null)
      setError('')
    }
  }, [user, reportId, isOpen])

  const getReportTypeIcon = (type: string) => {
    switch (type.toLowerCase()) {
      case 'sales': return <TrendingUp className="h-5 w-5" />
      case 'analytics': return <BarChart3 className="h-5 w-5" />
      case 'financial': return <DollarSign className="h-5 w-5" />
      case 'operational': return <Target className="h-5 w-5" />
      default: return <BarChart3 className="h-5 w-5" />
    }
  }

  const getStatusBadge = (status: string) => {
    const styles = {
      published: 'bg-green-100 text-green-800',
      draft: 'bg-yellow-100 text-yellow-800',
      archived: 'bg-gray-100 text-gray-800'
    }
    return (
      <Badge className={`${styles[status as keyof typeof styles] || styles.draft} hover:bg-current`}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    )
  }

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      size="xl"
      className="max-h-[90vh] overflow-y-auto"
    >
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          {report && (
            <>
              <div className="p-2 bg-gradient-to-br from-purple-500 to-pink-600 rounded-xl">
                {getReportTypeIcon(report.type)}
              </div>
              <div>
                <h2 className="text-xl font-bold text-gray-900">{report.name}</h2>
                <p className="text-sm text-gray-600">
                  Created {new Date(report.created_at).toLocaleDateString()}
                </p>
              </div>
            </>
          )}
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Button variant="outline" size="sm">
            <Share className="h-4 w-4 mr-2" />
            Share
          </Button>
          <Button variant="outline" size="sm">
            <Edit className="h-4 w-4 mr-2" />
            Edit
          </Button>
        </div>
      </div>

        <div className="space-y-6">
          {isLoading ? (
            <div className="text-center py-12">
              <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-gray-400" />
              <p className="text-sm text-gray-600">Loading report...</p>
            </div>
          ) : error ? (
            <div className="text-center py-12">
              <AlertCircle className="h-8 w-8 mx-auto mb-4 text-red-500" />
              <p className="text-lg font-medium text-gray-900 mb-2">Error Loading Report</p>
              <p className="text-sm text-gray-600">{error}</p>
            </div>
          ) : report ? (
            <>
              {/* Report Info */}
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <Card className="lg:col-span-2">
                  <CardHeader>
                    <CardTitle>Report Details</CardTitle>
                    <CardDescription>{report.description}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                      <div>
                        <p className="text-sm font-medium text-gray-500">Type</p>
                        <p className="text-lg font-semibold text-gray-900 capitalize">{report.type}</p>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-500">Status</p>
                        <div className="mt-1">{getStatusBadge(report.status)}</div>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-500">Last Updated</p>
                        <p className="text-lg font-semibold text-gray-900">
                          {new Date(report.updated_at).toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <Database className="h-5 w-5 mr-2" />
                      Data Source
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    {dataSource ? (
                      <div>
                        <p className="font-medium text-gray-900">{dataSource.name}</p>
                        <p className="text-sm text-gray-600 capitalize">{dataSource.type} data</p>
                        <p className="text-xs text-gray-500 mt-2">
                          Last synced: {dataSource.last_sync ? new Date(dataSource.last_sync).toLocaleDateString() : 'Never'}
                        </p>
                      </div>
                    ) : (
                      <p className="text-sm text-gray-500">Data source information unavailable</p>
                    )}
                  </CardContent>
                </Card>
              </div>

              {/* Report Content */}
              <ReportVisualization
                report={report}
                dataSource={dataSource}
              />
            </>
          ) : null}
        </div>
    </Modal>
  )
}
