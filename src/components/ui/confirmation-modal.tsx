'use client'

import * as React from 'react'
import { Modal } from './modal'
import { But<PERSON> } from './button'
import { AlertTriangle, Trash2, X, CheckCircle, Info, AlertCircle } from 'lucide-react'
import { cn } from '@/lib/utils'

interface ConfirmationModalProps {
  isOpen: boolean
  onClose: () => void
  onConfirm: () => void
  title: string
  description: string
  confirmText?: string
  cancelText?: string
  type?: 'danger' | 'warning' | 'info' | 'success'
  icon?: React.ReactNode
  loading?: boolean
}

export function ConfirmationModal({
  isOpen,
  onClose,
  onConfirm,
  title,
  description,
  confirmText = 'Confirm',
  cancelText = 'Cancel',
  type = 'danger',
  icon,
  loading = false
}: ConfirmationModalProps) {
  const getIcon = () => {
    if (icon) return icon
    
    switch (type) {
      case 'danger':
        return <Trash2 className="h-6 w-6 text-red-500" />
      case 'warning':
        return <AlertTriangle className="h-6 w-6 text-yellow-500" />
      case 'info':
        return <Info className="h-6 w-6 text-blue-500" />
      case 'success':
        return <CheckCircle className="h-6 w-6 text-green-500" />
      default:
        return <AlertCircle className="h-6 w-6 text-gray-500" />
    }
  }

  const getTypeStyles = () => {
    switch (type) {
      case 'danger':
        return {
          iconBg: 'bg-red-100',
          confirmButton: 'bg-red-600 hover:bg-red-700 focus:ring-red-500',
          titleColor: 'text-red-900'
        }
      case 'warning':
        return {
          iconBg: 'bg-yellow-100',
          confirmButton: 'bg-yellow-600 hover:bg-yellow-700 focus:ring-yellow-500',
          titleColor: 'text-yellow-900'
        }
      case 'info':
        return {
          iconBg: 'bg-blue-100',
          confirmButton: 'bg-blue-600 hover:bg-blue-700 focus:ring-blue-500',
          titleColor: 'text-blue-900'
        }
      case 'success':
        return {
          iconBg: 'bg-green-100',
          confirmButton: 'bg-green-600 hover:bg-green-700 focus:ring-green-500',
          titleColor: 'text-green-900'
        }
      default:
        return {
          iconBg: 'bg-gray-100',
          confirmButton: 'bg-gray-600 hover:bg-gray-700 focus:ring-gray-500',
          titleColor: 'text-gray-900'
        }
    }
  }

  const styles = getTypeStyles()

  const handleConfirm = () => {
    onConfirm()
    if (!loading) {
      onClose()
    }
  }

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      size="sm"
      showCloseButton={false}
      className="max-w-md"
    >
      <div className="text-center">
        {/* Icon */}
        <div className={cn(
          'mx-auto flex items-center justify-center h-12 w-12 rounded-full mb-4',
          styles.iconBg
        )}>
          {getIcon()}
        </div>

        {/* Title */}
        <h3 className={cn('text-lg font-semibold mb-2', styles.titleColor)}>
          {title}
        </h3>

        {/* Description */}
        <p className="text-gray-600 text-sm leading-relaxed mb-6">
          {description}
        </p>

        {/* Actions */}
        <div className="flex flex-col-reverse sm:flex-row sm:justify-center gap-3">
          <Button
            variant="outline"
            onClick={onClose}
            disabled={loading}
            className="flex-1 sm:flex-none px-6"
          >
            {cancelText}
          </Button>
          <Button
            onClick={handleConfirm}
            disabled={loading}
            className={cn(
              'flex-1 sm:flex-none px-6 text-white border-0 focus:ring-2 focus:ring-offset-2',
              styles.confirmButton
            )}
          >
            {loading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                Processing...
              </>
            ) : (
              confirmText
            )}
          </Button>
        </div>
      </div>
    </Modal>
  )
}

// Hook for easier usage
export function useConfirmation() {
  const [isOpen, setIsOpen] = React.useState(false)
  const [config, setConfig] = React.useState<Omit<ConfirmationModalProps, 'isOpen' | 'onClose' | 'onConfirm'>>({
    title: '',
    description: ''
  })
  const [onConfirmCallback, setOnConfirmCallback] = React.useState<(() => void) | null>(null)

  const confirm = React.useCallback((
    options: Omit<ConfirmationModalProps, 'isOpen' | 'onClose' | 'onConfirm'> & {
      onConfirm: () => void
    }
  ) => {
    const { onConfirm, ...rest } = options
    setConfig(rest)
    setOnConfirmCallback(() => onConfirm)
    setIsOpen(true)
  }, [])

  const handleClose = React.useCallback(() => {
    setIsOpen(false)
    setOnConfirmCallback(null)
  }, [])

  const handleConfirm = React.useCallback(() => {
    if (onConfirmCallback) {
      onConfirmCallback()
    }
    handleClose()
  }, [onConfirmCallback, handleClose])

  const ConfirmationComponent = React.useCallback(() => (
    <ConfirmationModal
      isOpen={isOpen}
      onClose={handleClose}
      onConfirm={handleConfirm}
      {...config}
    />
  ), [isOpen, handleClose, handleConfirm, config])

  return {
    confirm,
    ConfirmationModal: ConfirmationComponent
  }
}

// Convenience functions for common confirmation types
export const confirmations = {
  delete: (itemName: string, onConfirm: () => void) => ({
    title: 'Delete Item',
    description: `Are you sure you want to delete "${itemName}"? This action cannot be undone.`,
    confirmText: 'Delete',
    cancelText: 'Cancel',
    type: 'danger' as const,
    onConfirm
  }),
  
  remove: (itemName: string, onConfirm: () => void) => ({
    title: 'Remove Item',
    description: `Are you sure you want to remove "${itemName}"? You can add it back later.`,
    confirmText: 'Remove',
    cancelText: 'Cancel',
    type: 'warning' as const,
    onConfirm
  }),

  disconnect: (connectionName: string, onConfirm: () => void) => ({
    title: 'Disconnect Service',
    description: `Are you sure you want to disconnect from "${connectionName}"? This will stop all data synchronization.`,
    confirmText: 'Disconnect',
    cancelText: 'Cancel',
    type: 'warning' as const,
    onConfirm
  })
}
