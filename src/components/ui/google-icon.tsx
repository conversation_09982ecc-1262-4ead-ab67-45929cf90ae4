'use client'

import React from 'react'
import { cn } from '@/lib/utils'

interface GoogleIconProps {
  className?: string
  size?: number
  animated?: boolean
}

export function GoogleIcon({ className, size = 20, animated = true }: GoogleIconProps) {
  return (
    <div 
      className={cn(
        "relative inline-flex items-center justify-center",
        animated && "transition-all duration-300 hover:scale-110",
        className
      )}
      style={{ width: size, height: size }}
    >
      <svg
        width={size}
        height={size}
        viewBox="0 0 24 24"
        className={cn(
          "drop-shadow-sm",
          animated && "transition-all duration-300 hover:drop-shadow-md"
        )}
      >
        {/* Google G Logo */}
        <path
          d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
          fill="#4285F4"
          className={cn(
            animated && "transition-all duration-300 hover:fill-[#3367D6]"
          )}
        />
        <path
          d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
          fill="#34A853"
          className={cn(
            animated && "transition-all duration-300 hover:fill-[#2D8A47]"
          )}
        />
        <path
          d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
          fill="#FBBC05"
          className={cn(
            animated && "transition-all duration-300 hover:fill-[#F9AB00]"
          )}
        />
        <path
          d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
          fill="#EA4335"
          className={cn(
            animated && "transition-all duration-300 hover:fill-[#D33B2C]"
          )}
        />
        
        {/* Animated glow effect */}
        {animated && (
          <circle
            cx="12"
            cy="12"
            r="11"
            fill="none"
            stroke="rgba(66, 133, 244, 0.3)"
            strokeWidth="0"
            className="transition-all duration-300 hover:stroke-[1] hover:stroke-[rgba(66,133,244,0.5)]"
          />
        )}
      </svg>
      
      {/* Subtle pulse animation on hover */}
      {animated && (
        <div className="absolute inset-0 rounded-full bg-gradient-to-r from-blue-500/10 to-red-500/10 opacity-0 transition-all duration-300 hover:opacity-100 hover:scale-125 blur-sm" />
      )}
    </div>
  )
}

// Alternative simplified Google G icon
export function GoogleGIcon({ className, size = 20, animated = true }: GoogleIconProps) {
  return (
    <div 
      className={cn(
        "relative inline-flex items-center justify-center",
        animated && "transition-all duration-300 hover:scale-110",
        className
      )}
      style={{ width: size, height: size }}
    >
      <div
        className={cn(
          "relative rounded-full bg-white shadow-md flex items-center justify-center font-bold text-[#4285F4]",
          animated && "transition-all duration-300 hover:shadow-lg hover:bg-gray-50"
        )}
        style={{ 
          width: size, 
          height: size, 
          fontSize: size * 0.6,
          lineHeight: 1
        }}
      >
        G
        {/* Animated border */}
        {animated && (
          <div className="absolute inset-0 rounded-full border-2 border-transparent bg-gradient-to-r from-[#4285F4] via-[#34A853] via-[#FBBC05] to-[#EA4335] opacity-0 transition-all duration-300 hover:opacity-20 p-[1px]">
            <div className="w-full h-full rounded-full bg-white" />
          </div>
        )}
      </div>
    </div>
  )
}
