'use client'

import { useEffect, useRef, useState, useCallback } from 'react'

interface Circle {
  id: number
  x: number
  y: number
  size: number
  color: string
  opacity: number
  maxRadius: number
}

interface InteractiveBackgroundCirclesProps {
  className?: string
}

export function InteractiveBackgroundCircles({ className = '' }: InteractiveBackgroundCirclesProps) {
  const containerRef = useRef<HTMLDivElement>(null)
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 })
  const [isMouseInside, setIsMouseInside] = useState(false)
  const [containerDimensions, setContainerDimensions] = useState({ width: 0, height: 0 })

  // Define the circles with their original positions and properties
  const getCircles = (): Circle[] => [
    {
      id: 1,
      x: containerDimensions.width - 160, // -top-40 -right-40 equivalent (top-right)
      y: -160,
      size: 320, // w-80 h-80 equivalent
      color: 'bg-slate-300',
      opacity: 0.7,
      maxRadius: 60 // Maximum distance the circle can move from its original position
    },
    {
      id: 2,
      x: -160, // -bottom-40 -left-40 equivalent (bottom-left)
      y: containerDimensions.height - 160,
      size: 320,
      color: 'bg-gray-300',
      opacity: 0.7,
      maxRadius: 60
    },
    {
      id: 3,
      x: 160, // top-40 left-40 equivalent (top-left)
      y: 160,
      size: 320,
      color: 'bg-slate-400',
      opacity: 0.7,
      maxRadius: 60
    }
  ]

  useEffect(() => {
    const container = containerRef.current
    if (!container) return

    // Update container dimensions
    const updateDimensions = () => {
      const rect = container.getBoundingClientRect()
      setContainerDimensions({
        width: rect.width,
        height: rect.height
      })
    }

    // Initial dimensions
    updateDimensions()

    const handleMouseMove = (e: MouseEvent) => {
      const rect = container.getBoundingClientRect()
      setMousePosition({
        x: e.clientX - rect.left,
        y: e.clientY - rect.top
      })
    }

    const handleMouseEnter = () => {
      setIsMouseInside(true)
    }

    const handleMouseLeave = () => {
      setIsMouseInside(false)
    }

    const handleResize = () => {
      updateDimensions()
    }

    container.addEventListener('mousemove', handleMouseMove)
    container.addEventListener('mouseenter', handleMouseEnter)
    container.addEventListener('mouseleave', handleMouseLeave)
    window.addEventListener('resize', handleResize)

    return () => {
      container.removeEventListener('mousemove', handleMouseMove)
      container.removeEventListener('mouseenter', handleMouseEnter)
      container.removeEventListener('mouseleave', handleMouseLeave)
      window.removeEventListener('resize', handleResize)
    }
  }, [])

  const calculateCircleTransform = useCallback((circle: Circle) => {
    if (!isMouseInside || containerDimensions.width === 0) {
      return 'translate(0px, 0px) scale(1)'
    }

    // Calculate distance from mouse to circle center
    const circleCenterX = circle.x + circle.size / 2
    const circleCenterY = circle.y + circle.size / 2

    const deltaX = mousePosition.x - circleCenterX
    const deltaY = mousePosition.y - circleCenterY
    const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY)

    // Define the influence radius (how close the mouse needs to be to affect the circle)
    const influenceRadius = 200

    if (distance > influenceRadius) {
      return 'translate(0px, 0px) scale(1)'
    }

    // Calculate the influence factor (1 = full influence, 0 = no influence)
    const influenceFactor = Math.max(0, 1 - distance / influenceRadius)

    // Use easing function for smoother movement
    const easedInfluence = influenceFactor * influenceFactor * (3 - 2 * influenceFactor)

    // Calculate the movement direction (normalized)
    const moveX = distance > 0 ? (deltaX / distance) * circle.maxRadius * easedInfluence : 0
    const moveY = distance > 0 ? (deltaY / distance) * circle.maxRadius * easedInfluence : 0

    // Add a slight scale effect when mouse is very close
    const scaleEffect = 1 + (easedInfluence * 0.15)

    return `translate(${moveX}px, ${moveY}px) scale(${scaleEffect})`
  }, [isMouseInside, containerDimensions.width, mousePosition.x, mousePosition.y])

  const circles = getCircles()

  return (
    <div
      ref={containerRef}
      className={`absolute inset-0 overflow-hidden ${className}`}
    >
      {circles.map((circle) => (
        <div
          key={circle.id}
          className={`absolute ${circle.color} rounded-full mix-blend-multiply filter blur-xl interactive-circle ${
            isMouseInside ? 'following-mouse' : ''
          }`}
          style={{
            left: circle.x,
            top: circle.y,
            width: circle.size,
            height: circle.size,
            opacity: circle.opacity,
            transform: calculateCircleTransform(circle)
          }}
        />
      ))}
    </div>
  )
}
