export interface DataAnalysis {
  summary: {
    totalRows: number
    totalColumns: number
    numericColumns: string[]
    categoricalColumns: string[]
    dateColumns: string[]
    nullPercentage: number
  }
  insights: string[]
  recommendedCharts: ChartRecommendation[]
  statistics: Record<string, ColumnStatistics>
}

export interface ChartRecommendation {
  type: 'bar' | 'line' | 'pie' | 'area' | 'scatter' | 'histogram'
  title: string
  description: string
  xColumn?: string
  yColumn?: string
  priority: number
  reason: string
}

export interface ColumnStatistics {
  type: 'numeric' | 'categorical' | 'date'
  uniqueValues: number
  nullCount: number
  min?: number
  max?: number
  mean?: number
  median?: number
  mode?: string | number
  distribution?: Record<string, number>
}

export function analyzeData(data: Record<string, any>[], schema: Record<string, string>): DataAnalysis {
  if (!data.length) {
    return {
      summary: {
        totalRows: 0,
        totalColumns: 0,
        numericColumns: [],
        categoricalColumns: [],
        dateColumns: [],
        nullPercentage: 0
      },
      insights: ['No data available for analysis'],
      recommendedCharts: [],
      statistics: {}
    }
  }

  // Categorize columns
  const numericColumns = Object.keys(schema).filter(col => 
    schema[col] === 'number' || schema[col] === 'integer'
  )
  const categoricalColumns = Object.keys(schema).filter(col => 
    schema[col] === 'string'
  )
  const dateColumns = Object.keys(schema).filter(col => 
    schema[col] === 'date'
  )

  // Calculate statistics for each column
  const statistics: Record<string, ColumnStatistics> = {}
  const totalCells = data.length * Object.keys(schema).length
  let totalNulls = 0

  Object.keys(schema).forEach(column => {
    const values = data.map(row => row[column]).filter(val => val !== null && val !== undefined && val !== '')
    const nullCount = data.length - values.length
    totalNulls += nullCount

    if (schema[column] === 'number' || schema[column] === 'integer') {
      const numericValues = values.map(v => Number(v)).filter(v => !isNaN(v))
      const sorted = numericValues.sort((a, b) => a - b)
      
      statistics[column] = {
        type: 'numeric',
        uniqueValues: new Set(numericValues).size,
        nullCount,
        min: sorted.length > 0 ? sorted[0] : undefined,
        max: sorted.length > 0 ? sorted[sorted.length - 1] : undefined,
        mean: sorted.length > 0 ? sorted.reduce((a, b) => a + b, 0) / sorted.length : undefined,
        median: sorted.length > 0 ? sorted[Math.floor(sorted.length / 2)] : undefined
      }
    } else if (schema[column] === 'string') {
      const distribution = values.reduce((acc, val) => {
        acc[val] = (acc[val] || 0) + 1
        return acc
      }, {} as Record<string, number>)
      
      const mostCommon = Object.entries(distribution).sort(([,a], [,b]) => b - a)[0]
      
      statistics[column] = {
        type: 'categorical',
        uniqueValues: new Set(values).size,
        nullCount,
        mode: mostCommon ? mostCommon[0] : undefined,
        distribution
      }
    } else {
      statistics[column] = {
        type: 'date',
        uniqueValues: new Set(values).size,
        nullCount
      }
    }
  })

  const nullPercentage = (totalNulls / totalCells) * 100

  // Generate insights
  const insights = generateInsights(data, schema, statistics, numericColumns, categoricalColumns, dateColumns)

  // Generate chart recommendations
  const recommendedCharts = generateChartRecommendations(data, schema, statistics, numericColumns, categoricalColumns, dateColumns)

  return {
    summary: {
      totalRows: data.length,
      totalColumns: Object.keys(schema).length,
      numericColumns,
      categoricalColumns,
      dateColumns,
      nullPercentage
    },
    insights,
    recommendedCharts,
    statistics
  }
}

function generateInsights(
  data: Record<string, any>[], 
  schema: Record<string, string>,
  statistics: Record<string, ColumnStatistics>,
  numericColumns: string[],
  categoricalColumns: string[],
  dateColumns: string[]
): string[] {
  const insights = []

  // Basic data insights
  insights.push(`Dataset contains ${data.length.toLocaleString()} records across ${Object.keys(schema).length} columns`)

  if (numericColumns.length > 0) {
    insights.push(`Found ${numericColumns.length} numeric columns suitable for quantitative analysis`)
    
    // Find columns with high variance
    const highVarianceColumns = numericColumns.filter(col => {
      const stats = statistics[col]
      if (stats.min !== undefined && stats.max !== undefined && stats.mean !== undefined) {
        const range = stats.max - stats.min
        return range > stats.mean * 2 // High variance indicator
      }
      return false
    })
    
    if (highVarianceColumns.length > 0) {
      insights.push(`Columns with high variance detected: ${highVarianceColumns.join(', ')} - good for trend analysis`)
    }
  }

  if (categoricalColumns.length > 0) {
    insights.push(`Found ${categoricalColumns.length} categorical columns for segmentation and grouping`)
    
    // Find columns with reasonable number of categories for visualization
    const goodCategoricalColumns = categoricalColumns.filter(col => {
      const uniqueValues = statistics[col].uniqueValues
      return uniqueValues >= 2 && uniqueValues <= 20 // Sweet spot for visualization
    })
    
    if (goodCategoricalColumns.length > 0) {
      insights.push(`${goodCategoricalColumns.length} categorical columns are well-suited for charts`)
    }
  }

  if (dateColumns.length > 0) {
    insights.push(`Found ${dateColumns.length} date columns - perfect for time-series analysis`)
  }

  // Data quality insights
  const nullPercentage = Object.values(statistics).reduce((sum, stat) => sum + stat.nullCount, 0) / (data.length * Object.keys(schema).length) * 100
  
  if (nullPercentage > 10) {
    insights.push(`⚠️ ${nullPercentage.toFixed(1)}% of data is missing - consider data cleaning`)
  } else if (nullPercentage < 5) {
    insights.push(`✅ High data quality with only ${nullPercentage.toFixed(1)}% missing values`)
  }

  return insights
}

function generateChartRecommendations(
  data: Record<string, any>[], 
  schema: Record<string, string>,
  statistics: Record<string, ColumnStatistics>,
  numericColumns: string[],
  categoricalColumns: string[],
  dateColumns: string[]
): ChartRecommendation[] {
  const recommendations: ChartRecommendation[] = []

  // Bar chart recommendations
  if (categoricalColumns.length > 0 && numericColumns.length > 0) {
    const bestCategorical = categoricalColumns.find(col => 
      statistics[col].uniqueValues >= 2 && statistics[col].uniqueValues <= 15
    )
    const bestNumeric = numericColumns[0] // Use first numeric column
    
    if (bestCategorical && bestNumeric) {
      recommendations.push({
        type: 'bar',
        title: `${bestNumeric} by ${bestCategorical}`,
        description: `Compare ${bestNumeric} values across different ${bestCategorical} categories`,
        xColumn: bestCategorical,
        yColumn: bestNumeric,
        priority: 9,
        reason: 'Categorical vs Numeric comparison'
      })
    }
  }

  // Pie chart recommendations
  if (categoricalColumns.length > 0) {
    const bestCategorical = categoricalColumns.find(col => 
      statistics[col].uniqueValues >= 2 && statistics[col].uniqueValues <= 8
    )
    
    if (bestCategorical) {
      recommendations.push({
        type: 'pie',
        title: `Distribution of ${bestCategorical}`,
        description: `Show the proportion of each ${bestCategorical} category`,
        xColumn: bestCategorical,
        priority: 8,
        reason: 'Categorical distribution analysis'
      })
    }
  }

  // Line chart recommendations
  if (dateColumns.length > 0 && numericColumns.length > 0) {
    recommendations.push({
      type: 'line',
      title: `${numericColumns[0]} over Time`,
      description: `Track ${numericColumns[0]} trends over time`,
      xColumn: dateColumns[0],
      yColumn: numericColumns[0],
      priority: 10,
      reason: 'Time-series analysis'
    })
  } else if (numericColumns.length >= 2) {
    recommendations.push({
      type: 'line',
      title: `${numericColumns[1]} vs ${numericColumns[0]}`,
      description: `Show relationship between ${numericColumns[0]} and ${numericColumns[1]}`,
      xColumn: numericColumns[0],
      yColumn: numericColumns[1],
      priority: 7,
      reason: 'Numeric correlation analysis'
    })
  }

  // Area chart for time series
  if (dateColumns.length > 0 && numericColumns.length > 0) {
    recommendations.push({
      type: 'area',
      title: `${numericColumns[0]} Trend Area`,
      description: `Visualize ${numericColumns[0]} volume over time`,
      xColumn: dateColumns[0],
      yColumn: numericColumns[0],
      priority: 6,
      reason: 'Time-series volume analysis'
    })
  }

  // Sort by priority
  return recommendations.sort((a, b) => b.priority - a.priority)
}

export function prepareChartData(
  data: Record<string, any>[],
  type: string,
  xColumn?: string,
  yColumn?: string
): any[] {
  if (!data || !Array.isArray(data) || data.length === 0) {
    return [{ name: 'No Data', value: 0 }]
  }

  try {
    switch (type) {
      case 'bar':
      case 'line':
      case 'area':
        if (xColumn && yColumn) {
          // Group by X column and aggregate Y column
          const grouped = data.reduce((acc, row) => {
            if (!row || typeof row !== 'object') return acc
            const key = String(row[xColumn] || 'Unknown')
            if (!acc[key]) acc[key] = []
            const value = Number(row[yColumn]) || 0
            acc[key].push(value)
            return acc
          }, {} as Record<string, number[]>)

          const result = Object.entries(grouped).map(([key, values]) => ({
            name: key,
            value: values.length > 0 ? values.reduce((sum, val) => sum + val, 0) / values.length : 0, // Average
            total: values.reduce((sum, val) => sum + val, 0), // Sum
            count: values.length
          }))

          // Ensure we have at least some data
          return result.length > 0 ? result : [{ name: 'No Data', value: 0 }]
        } else {
          // Fallback when columns are not specified
          return [{ name: 'No Data', value: 0 }]
        }

      case 'pie':
        if (xColumn) {
          const counts = data.reduce((acc, row) => {
            if (!row || typeof row !== 'object') return acc
            const key = String(row[xColumn] || 'Unknown')
            acc[key] = (acc[key] || 0) + 1
            return acc
          }, {} as Record<string, number>)

          const result = Object.entries(counts).map(([key, value]) => ({
            name: key,
            value: value
          }))

          // Ensure we have at least some data
          return result.length > 0 ? result : [{ name: 'No Data', value: 1 }]
        } else {
          // Fallback when column is not specified
          return [{ name: 'No Data', value: 1 }]
        }

      default:
        return [{ name: 'Unsupported Chart Type', value: 0 }]
    }
  } catch (error) {
    console.error('Error preparing chart data:', error)
    return [{ name: 'Error', value: 0 }]
  }
}
