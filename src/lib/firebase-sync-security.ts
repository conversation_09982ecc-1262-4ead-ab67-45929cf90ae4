import { User } from 'firebase/auth'
import { doc, getDoc, collection, query, where, getDocs } from 'firebase/firestore'
import { db } from './firebase'
import { ExternalFirebaseConnection, CreateExternalFirebaseConnection } from './database.types'

/**
 * Security and validation service for Firebase sync operations
 */
export class FirebaseSyncSecurity {
  private static instance: FirebaseSyncSecurity | null = null

  private constructor() {}

  static getInstance(): FirebaseSyncSecurity {
    if (!FirebaseSyncSecurity.instance) {
      FirebaseSyncSecurity.instance = new FirebaseSyncSecurity()
    }
    return FirebaseSyncSecurity.instance
  }

  /**
   * Validate user permissions for connection operations
   */
  async validateUserPermissions(user: User, action: 'create' | 'read' | 'update' | 'delete', connectionId?: string): Promise<{
    allowed: boolean
    reason?: string
  }> {
    try {
      // Check if user is authenticated
      if (!user) {
        return { allowed: false, reason: 'User not authenticated' }
      }

      // For connection-specific actions, verify ownership
      if (connectionId && (action === 'read' || action === 'update' || action === 'delete')) {
        const connectionDoc = await getDoc(doc(db, 'external_firebase_connections', connectionId))
        
        if (!connectionDoc.exists()) {
          return { allowed: false, reason: 'Connection not found' }
        }

        const connection = connectionDoc.data() as ExternalFirebaseConnection
        if (connection.user_id !== user.uid) {
          return { allowed: false, reason: 'Access denied: Not the connection owner' }
        }
      }

      // Check rate limits for create operations
      if (action === 'create') {
        const rateLimitCheck = await this.checkRateLimit(user.uid)
        if (!rateLimitCheck.allowed) {
          return rateLimitCheck
        }
      }

      return { allowed: true }

    } catch (error) {
      console.error('Permission validation error:', error)
      return { allowed: false, reason: 'Permission validation failed' }
    }
  }

  /**
   * Check rate limits for connection creation
   */
  private async checkRateLimit(userId: string): Promise<{
    allowed: boolean
    reason?: string
  }> {
    try {
      // For development, we'll be more lenient with rate limiting
      // In production, you might want to make this more strict

      // Check total connections limit only (skip hourly rate limit for now)
      const totalConnectionsQuery = query(
        collection(db, 'external_firebase_connections'),
        where('user_id', '==', userId)
      )

      const totalConnections = await getDocs(totalConnectionsQuery)
      const maxTotalConnections = 50 // Increased limit for development

      if (totalConnections.size >= maxTotalConnections) {
        return {
          allowed: false,
          reason: `Connection limit exceeded: Maximum ${maxTotalConnections} connections allowed`
        }
      }

      return { allowed: true }

    } catch (error) {
      console.error('Rate limit check error:', error instanceof Error ? error.message : String(error))
      // In case of Firestore connection issues, allow the operation to proceed
      // but log the error for debugging
      console.warn('Rate limit check failed, allowing operation to proceed for development')
      return { allowed: true }
    }
  }

  /**
   * Validate Firebase configuration
   */
  validateFirebaseConfig(config: CreateExternalFirebaseConnection['config']): {
    valid: boolean
    errors: string[]
  } {
    const errors: string[] = []

    // Required fields validation
    const requiredFields = ['apiKey', 'authDomain', 'projectId', 'storageBucket', 'messagingSenderId', 'appId']
    
    for (const field of requiredFields) {
      if (!config[field as keyof typeof config] || typeof config[field as keyof typeof config] !== 'string') {
        errors.push(`${field} is required and must be a string`)
      }
    }

    // Format validation
    if (config.apiKey && !config.apiKey.startsWith('AIza')) {
      errors.push('API key must start with "AIza"')
    }

    if (config.authDomain && !config.authDomain.includes('.firebaseapp.com')) {
      errors.push('Auth domain must be a valid Firebase auth domain')
    }

    if (config.projectId && !/^[a-z0-9-]+$/.test(config.projectId)) {
      errors.push('Project ID must contain only lowercase letters, numbers, and hyphens')
    }

    if (config.storageBucket && !(config.storageBucket.includes('.appspot.com') || config.storageBucket.includes('.firebasestorage.app'))) {
      errors.push('Storage bucket must be a valid Firebase storage bucket')
    }

    if (config.messagingSenderId && !/^\d+$/.test(config.messagingSenderId)) {
      errors.push('Messaging sender ID must be numeric')
    }

    if (config.appId && !config.appId.includes(':web:')) {
      errors.push('App ID must be a valid Firebase web app ID')
    }

    if (config.measurementId && config.measurementId && !config.measurementId.startsWith('G-')) {
      errors.push('Measurement ID must start with "G-"')
    }

    return {
      valid: errors.length === 0,
      errors
    }
  }

  /**
   * Validate sync settings
   */
  validateSyncSettings(settings: CreateExternalFirebaseConnection['sync_settings']): {
    valid: boolean
    errors: string[]
  } {
    const errors: string[] = []

    if (!settings) {
      errors.push('Sync settings are required')
      return { valid: false, errors }
    }

    // Validate collections
    if (!settings.collections || settings.collections.length === 0) {
      errors.push('At least one collection must be specified')
    } else {
      // Validate collection names
      for (const collection of settings.collections) {
        if (typeof collection !== 'string' || collection.trim().length === 0) {
          errors.push('Collection names must be non-empty strings')
          break
        }
        
        // Check for invalid characters in collection names
        if (!/^[a-zA-Z][a-zA-Z0-9_]*$/.test(collection)) {
          errors.push(`Invalid collection name "${collection}": must start with a letter and contain only letters, numbers, and underscores`)
        }
      }
    }

    // Validate sync direction
    const validDirections = ['pull', 'push', 'bidirectional']
    if (settings.sync_direction && !validDirections.includes(settings.sync_direction)) {
      errors.push(`Invalid sync direction: must be one of ${validDirections.join(', ')}`)
    }

    // Validate sync frequency
    const validFrequencies = ['realtime', 'hourly', 'daily', 'manual']
    if (settings.sync_frequency && !validFrequencies.includes(settings.sync_frequency)) {
      errors.push(`Invalid sync frequency: must be one of ${validFrequencies.join(', ')}`)
    }

    // Validate conflict resolution
    const validResolutions = ['source_wins', 'target_wins', 'merge', 'manual']
    if (settings.conflict_resolution && !validResolutions.includes(settings.conflict_resolution)) {
      errors.push(`Invalid conflict resolution: must be one of ${validResolutions.join(', ')}`)
    }

    return {
      valid: errors.length === 0,
      errors
    }
  }

  /**
   * Sanitize connection data before storage
   */
  sanitizeConnectionData(data: CreateExternalFirebaseConnection): CreateExternalFirebaseConnection {
    return {
      ...data,
      name: data.name.trim().substring(0, 100), // Limit name length
      description: data.description?.trim().substring(0, 500) || '', // Limit description length
      project_id: data.project_id.trim().toLowerCase(),
      config: {
        ...data.config,
        apiKey: data.config.apiKey.trim(),
        authDomain: data.config.authDomain.trim().toLowerCase(),
        projectId: data.config.projectId.trim().toLowerCase(),
        storageBucket: data.config.storageBucket.trim().toLowerCase(),
        messagingSenderId: data.config.messagingSenderId.trim(),
        appId: data.config.appId.trim(),
        measurementId: data.config.measurementId?.trim() || ''
      },
      sync_settings: {
        ...data.sync_settings,
        collections: data.sync_settings?.collections?.map(c => c.trim()).filter(c => c.length > 0) || []
      }
    }
  }

  /**
   * Check if a project ID is already connected by the user
   */
  async checkDuplicateConnection(userId: string, projectId: string): Promise<{
    isDuplicate: boolean
    existingConnectionId?: string
  }> {
    try {
      const duplicateQuery = query(
        collection(db, 'external_firebase_connections'),
        where('user_id', '==', userId),
        where('project_id', '==', projectId.toLowerCase())
      )

      const duplicateConnections = await getDocs(duplicateQuery)
      
      if (duplicateConnections.size > 0) {
        return {
          isDuplicate: true,
          existingConnectionId: duplicateConnections.docs[0].id
        }
      }

      return { isDuplicate: false }

    } catch (error) {
      console.error('Duplicate connection check error:', error)
      return { isDuplicate: false }
    }
  }

  /**
   * Validate connection data comprehensively
   */
  async validateConnectionData(
    user: User,
    data: CreateExternalFirebaseConnection,
    action: 'create' | 'update' = 'create'
  ): Promise<{
    valid: boolean
    errors: string[]
    warnings: string[]
  }> {
    const errors: string[] = []
    const warnings: string[] = []

    // Check user permissions
    const permissionCheck = await this.validateUserPermissions(user, action)
    if (!permissionCheck.allowed) {
      errors.push(permissionCheck.reason || 'Permission denied')
    }

    // Validate Firebase config
    const configValidation = this.validateFirebaseConfig(data.config)
    if (!configValidation.valid) {
      errors.push(...configValidation.errors)
    }

    // Validate sync settings
    const syncValidation = this.validateSyncSettings(data.sync_settings)
    if (!syncValidation.valid) {
      errors.push(...syncValidation.errors)
    }

    // Check for duplicate connections (only for create)
    if (action === 'create') {
      const duplicateCheck = await this.checkDuplicateConnection(user.uid, data.project_id)
      if (duplicateCheck.isDuplicate) {
        errors.push(`A connection to project "${data.project_id}" already exists`)
      }
    }

    // Add warnings for potentially risky configurations
    if (data.sync_settings?.sync_direction === 'bidirectional') {
      warnings.push('Bidirectional sync can lead to data conflicts. Ensure proper conflict resolution is configured.')
    }

    if (data.sync_settings?.sync_frequency === 'realtime' && data.sync_settings.collections && data.sync_settings.collections.length > 5) {
      warnings.push('Real-time sync with many collections may impact performance. Consider using scheduled sync.')
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings
    }
  }
}

// Export singleton instance
export const firebaseSyncSecurity = FirebaseSyncSecurity.getInstance()
