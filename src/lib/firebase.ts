import { initializeApp, getApps } from 'firebase/app'
import { getAuth } from 'firebase/auth'
import { getFirestore } from 'firebase/firestore'
import { getStorage } from 'firebase/storage'
import { getAnalytics, isSupported } from 'firebase/analytics'



const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID,
  measurementId: process.env.NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID,
}



// Initialize Firebase
const app = getApps().length === 0 ? initializeApp(firebaseConfig) : getApps()[0]

// Initialize Firebase services
export const auth = getAuth(app)
export const db = getFirestore(app)
export const storage = getStorage(app)

// Initialize Analytics (only in browser and if supported)
export let analytics: ReturnType<typeof getAnalytics> | null = null

// Only initialize analytics in production or when explicitly enabled
const shouldInitializeAnalytics =
  process.env.NODE_ENV === 'production' ||
  process.env.NEXT_PUBLIC_ENABLE_ANALYTICS === 'true'

if (typeof window !== 'undefined' && shouldInitializeAnalytics) {
  isSupported().then((supported) => {
    if (supported) {
      analytics = getAnalytics(app)
      console.log('Firebase Analytics initialized')
    }
  }).catch((error) => {
    console.log('Analytics not supported:', error)
  })
} else if (process.env.NODE_ENV === 'development') {
  console.log('Analytics disabled in development. Set NEXT_PUBLIC_ENABLE_ANALYTICS=true to enable.')
}

// Note: Firebase emulator configuration removed - now using production Firebase services

export default app
