import { initializeApp, FirebaseApp, getApps } from 'firebase/app'
import { getFirestore, Firestore } from 'firebase/firestore'
import { getAuth, Auth } from 'firebase/auth'
import { ExternalFirebaseConnection } from './database.types'

interface FirebaseConnection {
  app: FirebaseApp
  db: Firestore
  auth: Auth
  config: ExternalFirebaseConnection
}

/**
 * Multi-Firebase Connection Manager
 * Manages connections to multiple Firebase projects for real-time data sync
 */
export class MultiFirebaseManager {
  private connections: Map<string, FirebaseConnection> = new Map()
  private static instance: MultiFirebaseManager | null = null

  private constructor() {}

  static getInstance(): MultiFirebaseManager {
    if (!MultiFirebaseManager.instance) {
      MultiFirebaseManager.instance = new MultiFirebaseManager()
    }
    return MultiFirebaseManager.instance
  }

  /**
   * Add a new Firebase connection
   */
  async addConnection(connectionConfig: ExternalFirebaseConnection): Promise<boolean> {
    try {
      const { id, config } = connectionConfig

      console.log(`🔗 Adding Firebase connection: ${connectionConfig.name} (${id})`)
      console.log('Connection config:', {
        projectId: config.projectId,
        authDomain: config.authDomain,
        apiKey: config.apiKey ? `${config.apiKey.substring(0, 10)}...` : 'missing',
        appId: config.appId ? `${config.appId.substring(0, 10)}...` : 'missing'
      })

      // Check if connection already exists
      if (this.connections.has(id)) {
        console.warn(`⚠️ Connection ${id} already exists`)
        return false
      }

      // Create Firebase app with unique name
      const appName = `external-${id}`

      // Check if app already exists
      const existingApp = getApps().find(app => app.name === appName)
      if (existingApp) {
        console.warn(`⚠️ Firebase app ${appName} already exists`)
        return false
      }

      console.log(`🚀 Initializing Firebase app: ${appName}`)

      // Initialize Firebase app
      const app = initializeApp(config, appName)
      const db = getFirestore(app)
      const auth = getAuth(app)

      console.log(`✅ Firebase app initialized successfully: ${appName}`)

      // Store connection
      this.connections.set(id, {
        app,
        db,
        auth,
        config: connectionConfig
      })

      console.log(`✅ Connection ${connectionConfig.name} added successfully`)
      return true

    } catch (error) {
      console.error(`❌ Failed to add Firebase connection ${connectionConfig.name}:`, error)
      return false
    }
  }

  /**
   * Remove a Firebase connection
   */
  async removeConnection(connectionId: string): Promise<boolean> {
    try {
      const connection = this.connections.get(connectionId)
      if (!connection) {
        console.warn(`Connection ${connectionId} not found`)
        return false
      }

      // Delete the Firebase app
      await connection.app.delete()
      
      // Remove from connections map
      this.connections.delete(connectionId)

      return true

    } catch (error) {
      console.error(`Failed to remove Firebase connection:`, error)
      return false
    }
  }

  /**
   * Get a specific Firebase connection
   */
  getConnection(connectionId: string): FirebaseConnection | null {
    return this.connections.get(connectionId) || null
  }

  /**
   * Get all active connections
   */
  getAllConnections(): Map<string, FirebaseConnection> {
    return new Map(this.connections)
  }

  /**
   * Test connection to external Firebase project
   */
  async testConnection(connectionConfig: ExternalFirebaseConnection): Promise<{
    success: boolean
    error?: string
    details?: Record<string, unknown>
  }> {
    let testApp: FirebaseApp | null = null
    
    try {
      const testAppName = `test-${connectionConfig.id}-${Date.now()}`
      
      // Initialize test Firebase app
      testApp = initializeApp(connectionConfig.config, testAppName)
      const testDb = getFirestore(testApp)

      // Try to read from a collection (this will fail if permissions are wrong)
      // We'll try to read from a common collection or create a test document
      const testCollection = 'test_connection'
      
      // Attempt to read (this will validate the connection and permissions)
      await testDb.collection(testCollection).limit(1).get()

      return {
        success: true,
        details: {
          projectId: connectionConfig.config.projectId,
          timestamp: new Date().toISOString()
        }
      }

    } catch (error) {
      console.error('Connection test failed:', error)
      
      let errorMessage = 'Unknown error'
      if (error && typeof error === 'object' && 'code' in error) {
        if (error.code === 'permission-denied') {
          errorMessage = 'Permission denied. Check Firestore security rules.'
        } else if (error.code === 'invalid-api-key') {
          errorMessage = 'Invalid API key'
        } else if (error.code === 'project-not-found') {
          errorMessage = 'Firebase project not found'
        }
      } else if (error instanceof Error && error.message) {
        errorMessage = error.message
      }

      return {
        success: false,
        error: errorMessage,
        details: {
          code: error.code,
          message: error.message
        }
      }

    } finally {
      // Clean up test app
      if (testApp) {
        try {
          await testApp.delete()
        } catch (cleanupError) {
          console.warn('Failed to cleanup test app:', cleanupError)
        }
      }
    }
  }

  /**
   * Update connection configuration
   */
  async updateConnection(connectionId: string, updates: Partial<ExternalFirebaseConnection>): Promise<boolean> {
    try {
      const connection = this.connections.get(connectionId)
      if (!connection) {
        console.warn(`Connection ${connectionId} not found`)
        return false
      }

      // If config is being updated, we need to recreate the connection
      if (updates.config) {
        await this.removeConnection(connectionId)
        const updatedConfig = { ...connection.config, ...updates }
        return await this.addConnection(updatedConfig)
      }

      // Update the stored config
      connection.config = { ...connection.config, ...updates }
      return true

    } catch (error) {
      console.error(`❌ Failed to update Firebase connection:`, error)
      return false
    }
  }

  /**
   * Get connection status
   */
  getConnectionStatus(connectionId: string): 'connected' | 'disconnected' | 'error' | 'not_found' {
    const connection = this.connections.get(connectionId)
    if (!connection) {
      return 'not_found'
    }

    try {
      // Check if the app is still valid
      if (connection.app && connection.db) {
        return 'connected'
      }
      return 'disconnected'
    } catch {
      return 'error'
    }
  }

  /**
   * Initialize connections from stored configurations
   */
  async initializeStoredConnections(connections: ExternalFirebaseConnection[]): Promise<void> {
    for (const connectionConfig of connections) {
      if (connectionConfig.status === 'active') {
        await this.addConnection(connectionConfig)
      }
    }
  }

  /**
   * Cleanup all connections
   */
  async cleanup(): Promise<void> {
    const connectionIds = Array.from(this.connections.keys())
    for (const connectionId of connectionIds) {
      await this.removeConnection(connectionId)
    }
  }
}

// Export singleton instance
export const multiFirebaseManager = MultiFirebaseManager.getInstance()

// Export types
export type { FirebaseConnection }
