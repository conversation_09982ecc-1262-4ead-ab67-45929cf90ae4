// This file is kept for backward compatibility during migration
// All new code should use Firebase services directly

import { auth, db, storage } from './firebase'
import firestoreService from './firestore'

// Legacy exports for backward compatibility
export const supabase = {
  auth: {
    getSession: () => Promise.resolve({ data: { session: null } }),
    signInWithPassword: () => Promise.resolve({ error: new Error('Use Firebase Auth instead') }),
    signUp: () => Promise.resolve({ error: new Error('Use Firebase Auth instead') }),
    signOut: () => Promise.resolve({ error: new Error('Use Firebase Auth instead') }),
    resetPasswordForEmail: () => Promise.resolve({ error: new Error('Use Firebase Auth instead') }),
    onAuthStateChange: () => ({ data: { subscription: { unsubscribe: () => {} } } }),
  },
  from: () => ({
    select: () => Promise.resolve({ data: null, error: new Error('Use Firestore instead') }),
    insert: () => Promise.resolve({ data: null, error: new Error('Use Firestore instead') }),
    update: () => Promise.resolve({ data: null, error: new Error('Use Firestore instead') }),
    delete: () => Promise.resolve({ data: null, error: new Error('Use Firestore instead') }),
  }),
}

// New Firebase exports
export { auth, db, storage }
export default firestoreService

// Client-side Firebase client (same as default)
export const createClientComponentClient = () => {
  return firestoreService
}

// Server-side Firebase client (same as default for now)
export const createServerComponentClient = () => {
  return firestoreService
}
