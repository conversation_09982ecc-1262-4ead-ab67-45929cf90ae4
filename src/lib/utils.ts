import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatBytes(bytes: number, decimals = 2) {
  if (bytes === 0) return '0 Bytes'

  const k = 1024
  const dm = decimals < 0 ? 0 : decimals
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']

  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i]
}

export function formatNumber(num: number) {
  return new Intl.NumberFormat().format(num)
}

export function formatDate(date: string | Date) {
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  }).format(new Date(date))
}

export function formatRelativeTime(date: string | Date) {
  const now = new Date()
  const targetDate = new Date(date)
  const diffInSeconds = Math.floor((now.getTime() - targetDate.getTime()) / 1000)

  if (diffInSeconds < 60) {
    return 'just now'
  } else if (diffInSeconds < 3600) {
    const minutes = Math.floor(diffInSeconds / 60)
    return `${minutes} minute${minutes > 1 ? 's' : ''} ago`
  } else if (diffInSeconds < 86400) {
    const hours = Math.floor(diffInSeconds / 3600)
    return `${hours} hour${hours > 1 ? 's' : ''} ago`
  } else if (diffInSeconds < 2592000) {
    const days = Math.floor(diffInSeconds / 86400)
    return `${days} day${days > 1 ? 's' : ''} ago`
  } else {
    return formatDate(date)
  }
}

export function generateId() {
  return Math.random().toString(36).substring(2) + Date.now().toString(36)
}

export function slugify(text: string) {
  return text
    .toLowerCase()
    .replace(/[^\w ]+/g, '')
    .replace(/ +/g, '-')
}

export function truncateText(text: string, maxLength: number) {
  if (text.length <= maxLength) return text
  return text.substring(0, maxLength) + '...'
}

export function isValidEmail(email: string) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

export function debounce<T extends (...args: unknown[]) => unknown>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null
  
  return (...args: Parameters<T>) => {
    if (timeout) clearTimeout(timeout)
    timeout = setTimeout(() => func(...args), wait)
  }
}

export function throttle<T extends (...args: unknown[]) => unknown>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}

export function downloadFile(data: string, filename: string, type: string) {
  const blob = new Blob([data], { type })
  const url = window.URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = filename
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  window.URL.revokeObjectURL(url)
}

export function copyToClipboard(text: string) {
  if (navigator.clipboard) {
    return navigator.clipboard.writeText(text)
  } else {
    // Fallback for older browsers
    const textArea = document.createElement('textarea')
    textArea.value = text
    document.body.appendChild(textArea)
    textArea.focus()
    textArea.select()
    try {
      document.execCommand('copy')
    } catch (err) {
      console.error('Failed to copy text: ', err)
    }
    document.body.removeChild(textArea)
    return Promise.resolve()
  }
}

export function getFileExtension(filename: string) {
  return filename.slice((filename.lastIndexOf('.') - 1 >>> 0) + 2)
}

export function isValidFileType(filename: string, allowedTypes: string[]) {
  const extension = getFileExtension(filename).toLowerCase()
  return allowedTypes.includes(extension)
}

export function parseCSV(csvText: string) {
  const lines = csvText.split('\n')
  const headers = lines[0].split(',').map(header => header.trim().replace(/"/g, ''))
  const data = []

  for (let i = 1; i < lines.length; i++) {
    if (lines[i].trim()) {
      const values = lines[i].split(',').map(value => value.trim().replace(/"/g, ''))
      const row: Record<string, string> = {}
      headers.forEach((header, index) => {
        row[header] = values[index] || ''
      })
      data.push(row)
    }
  }

  return { headers, data }
}

export function detectDataTypes(data: Record<string, unknown>[]) {
  if (!data.length) return {}

  const types: Record<string, string> = {}
  const sample = data.slice(0, Math.min(100, data.length)) // Sample first 100 rows

  Object.keys(data[0]).forEach(column => {
    const values = sample.map(row => row[column]).filter(val => val !== null && val !== '')
    
    if (values.length === 0) {
      types[column] = 'string'
      return
    }

    // Check if all values are numbers
    const numericValues = values.filter(val => !isNaN(Number(val)))
    if (numericValues.length === values.length) {
      // Check if all numbers are integers
      const integerValues = numericValues.filter(val => Number.isInteger(Number(val)))
      types[column] = integerValues.length === numericValues.length ? 'integer' : 'number'
      return
    }

    // Check if all values are dates
    const dateValues = values.filter(val => !isNaN(Date.parse(val)))
    if (dateValues.length === values.length) {
      types[column] = 'date'
      return
    }

    // Check if all values are booleans
    const booleanValues = values.filter(val => 
      val.toLowerCase() === 'true' || 
      val.toLowerCase() === 'false' || 
      val === '1' || 
      val === '0'
    )
    if (booleanValues.length === values.length) {
      types[column] = 'boolean'
      return
    }

    // Default to string
    types[column] = 'string'
  })

  return types
}

export function validateData(data: Record<string, unknown>[], schema: Record<string, string>) {
  const errors: string[] = []
  const warnings: string[] = []

  if (!data.length) {
    errors.push('Dataset is empty')
    return { errors, warnings, isValid: false }
  }

  // Check for missing columns
  const dataColumns = Object.keys(data[0])
  const schemaColumns = Object.keys(schema)
  
  const missingColumns = schemaColumns.filter(col => !dataColumns.includes(col))
  if (missingColumns.length > 0) {
    errors.push(`Missing columns: ${missingColumns.join(', ')}`)
  }

  // Check data quality
  let nullCount = 0
  let totalCells = 0

  data.forEach((row, rowIndex) => {
    Object.entries(row).forEach(([column, value]) => {
      totalCells++
      
      if (value === null || value === undefined || value === '') {
        nullCount++
      }
      
      // Type validation
      const expectedType = schema[column]
      if (expectedType && value !== null && value !== undefined && value !== '') {
        switch (expectedType) {
          case 'number':
          case 'integer':
            if (isNaN(Number(value))) {
              errors.push(`Row ${rowIndex + 1}, Column "${column}": Expected ${expectedType}, got "${value}"`)
            }
            break
          case 'date':
            if (isNaN(Date.parse(value))) {
              errors.push(`Row ${rowIndex + 1}, Column "${column}": Expected date, got "${value}"`)
            }
            break
          case 'boolean':
            const boolValue = value.toString().toLowerCase()
            if (!['true', 'false', '1', '0'].includes(boolValue)) {
              errors.push(`Row ${rowIndex + 1}, Column "${column}": Expected boolean, got "${value}"`)
            }
            break
        }
      }
    })
  })

  // Data quality warnings
  const nullPercentage = (nullCount / totalCells) * 100
  if (nullPercentage > 10) {
    warnings.push(`High percentage of missing values: ${nullPercentage.toFixed(1)}%`)
  }

  if (data.length < 10) {
    warnings.push('Small dataset size may affect analysis quality')
  }

  return {
    errors,
    warnings,
    isValid: errors.length === 0,
    stats: {
      totalRows: data.length,
      totalColumns: dataColumns.length,
      nullPercentage: nullPercentage.toFixed(1)
    }
  }
}
