#!/bin/bash

# Load nvm
export NVM_DIR="$HOME/.nvm"
[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"

echo "🔄 Updating packages to latest versions..."

# Remove insecure xlsx package and install secure alternative
echo "📦 Replacing xlsx with @sheetjs/xlsx..."
npm uninstall xlsx
npm install @sheetjs/xlsx@latest

# Update other key packages
echo "📦 Updating core packages..."
npm install @types/node@latest
npm install lucide-react@latest
npm install react@latest react-dom@latest
npm install next@latest
npm install firebase@latest
npm install firebase-admin@latest
npm install typescript@latest

echo "✅ Package updates completed!"
echo "🔍 Checking for remaining vulnerabilities..."
npm audit

echo "📊 Current versions:"
echo "Node.js: $(node --version)"
echo "npm: $(npm --version)"
echo "Next.js: $(npm list next --depth=0 2>/dev/null | grep next)"
echo "React: $(npm list react --depth=0 2>/dev/null | grep react)"
echo "TypeScript: $(npm list typescript --depth=0 2>/dev/null | grep typescript)"
